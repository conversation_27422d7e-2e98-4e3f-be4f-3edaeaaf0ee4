# LLM不确定性分析系统配置文件

# 模型配置
model:
  name: "deepseek-reasoner"  # 使用deepseek模型
  base_url: "https://api.deepseek.com/v1"
  api_key_env: "DEEPSEEK_API_KEY"
  temperature: 1.3
  top_p: 0.95
  max_tokens: null
  enable_thinking: true
  enable_logprobs: false  # deepseek-reasoner不支持logprobs
  top_logprobs: 5
  stream: false  # 使用非stream模式

# 输出配置
output:
  format: "mongodb"  # mongodb, csv, json
  # MongoDB设置
  mongo:
    host: "localhost"
    port: 27017
    database: "LLM-UQ"
    collection: "response_collections"
    test_collection: "test_response"
  # 文件输出设置
  file:
    output_dir: "output"
    filename_prefix: "llm_responses"

# 日志配置
logging:
  level: "INFO"
  file: "llm_response_generator.log"

# 任务配置
tasks:
  sentiment_analysis:
    enabled: true  # 启用情感分析任务
    name: "sentiment_analysis"
    task_category: "sentiment_analysis"
    dataset_source: "twitter_sentiment"
    data_file: "sampled_semeval.csv"
    prompt_dir: "prompts/1_sentiment_analysis"
    num_prompts: 10
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 6  # 每个prompt重复6次
    template_variable: "tweet"  # prompt中的占位符
    id_field: "id"
    text_field: "text"
    label_field: "label"

  explorative_coding:
    enabled: true  # 启用代码探索性分析任务
    name: "explorative_coding"
    task_category: "open_explorative_coding"
    dataset_source: "pytorch_commits"
    data_file: "sampled_commits.csv"
    prompt_dir: "prompts/2_explorative_coding_commits"
    num_prompts: 10
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 6  # 每个prompt重复6次
    template_variables:
      repo_name: "pytorch/pytorch"
      message: "message"
    id_field: "sha"
    text_field: "message"
    label_field: null

  topic_labeling:
    enabled: true
    name: "topic_labeling"
    task_category: "topic_modeling_labeling"
    dataset_source: "topic_model_data"
    data_file: "data/topic_modeling/topic-model-topwords-label.csv"
    prompt_dir: "prompts/3_topic_labeling"
    num_prompts: 11  # 使用原有的11个prompt模板
    sample_prompts: 5  # 从11个中随机选择5个
    attempts_per_prompt: 6  # 每个prompt重复6次（保持与原代码一致）
    template_variable: "key_terms"  # prompt中的占位符
    id_field: "topic_number"  # 使用topic_number作为ID
    text_field: "key_terms"   # 关键词字段
    label_field: "original_label"  # 原始标签字段
    # 额外的topic modeling特定配置
    multi_model_support: true  # 支持多模型对比
    enable_uncertainty_analysis: true  # 启用不确定性分析

  counterfactual_qa:
    enabled: true
    name: "counterfactual_qa"
    task_category: "historical_counterfactual_qa"
    dataset_source: "counterfactual_data"
    data_file: "data/conterfactual/counterfactual_prompts.csv"
    prompt_dir: "prompts/4_counterfactual_qa"
    num_prompts: 10  # 创建10个不同的历史分析prompt模板
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 6  # 每个prompt重复6次
    template_variable: "question"  # prompt中的占位符
    id_field: "Category"  # 使用Category作为分组ID
    text_field: "Prompt"   # 问题字段
    label_field: null  # 没有参考答案
    # 额外的counterfactual特定配置
    multi_model_support: true  # 支持多模型对比
    enable_uncertainty_analysis: true  # 启用不确定性分析

# 系统配置
system:
  max_concurrent_requests: 10
  max_retries: 3
  retry_delay: 1.0
  enable_resume: true
  test_mode_sample_size: 1

"""
NLI计算模块 - 统一的自然语言推理计算和缓存管理

该模块提供：
1. 统一的NLI模型加载和计算
2. 完整的三分数计算 (entailment, neutral, contradiction)
3. 缓存管理
4. 向后兼容性
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from typing import List, Tuple, NamedTuple, Optional
import hashlib
import time
import logging
import os
import pandas as pd
import sqlite3

logger = logging.getLogger(__name__)

class NLIResult(NamedTuple):
    """NLI计算结果，包含三个分数和原始logits"""
    entailment: float
    neutral: float
    contradiction: float
    logits: Optional[List[float]] = None


class NLICalculator:
    """统一的NLI计算器"""
    
    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False):
        """
        初始化NLI计算器
        
        Args:
            model_name: NLI模型名称
            verbose: 是否输出详细信息
        """
        self.model_name = model_name
        self.verbose = verbose
        
        # 初始化设备和模型
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if self.verbose:
            logger.info(f"Loading NLI model: {model_name} on {self.device}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()
        
        if self.verbose:
            logger.info(f"NLI model loaded successfully")
    
    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """
        计算两个文本之间的完整NLI分数
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        try:
            inputs = self.tokenizer(
                text1, text2, 
                return_tensors="pt", 
                truncation=True, 
                max_length=256
            ).to(self.device)
            
            with torch.no_grad():
                logits = self.model(**inputs).logits
                probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
                raw_logits = logits.cpu().numpy()[0]

            # Label mapping: 0=contradiction, 1=neutral, 2=entailment
            contradiction_score = float(probs[0])
            neutral_score = float(probs[1])
            entailment_score = float(probs[2])

            return NLIResult(
                entailment=entailment_score,
                neutral=neutral_score,
                contradiction=contradiction_score,
                logits=raw_logits.tolist()
            )
            
        except Exception as e:
            logger.warning(f"Error computing NLI scores for model {self.model_name}: {str(e)}")
            # 返回均匀分布作为默认值
            return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)
    
    def compute_entailment_score(self, text1: str, text2: str) -> float:
        """
        计算entailment分数（向后兼容）
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            float: entailment分数
        """
        nli_result = self.compute_nli_scores(text1, text2)
        return nli_result.entailment
    
    def compute_similarity_matrix(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """
        计算响应列表的相似度矩阵
        
        Args:
            responses: 响应文本列表
            use_score: 使用的分数类型 ("entailment", "neutral", "contradiction")
            
        Returns:
            numpy.ndarray: 相似度矩阵
        """
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 计算双向分数并取平均
                    nli_ij = self.compute_nli_scores(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    @staticmethod
    def get_text_hash(text: str, algorithm: str = 'sha256') -> str:
        """
        生成文本的哈希值

        Args:
            text: 要hash的文本
            algorithm: hash算法 ('md5', 'sha256', 'sha1')

        Returns:
            str: 十六进制hash值
        """
        if algorithm == 'md5':
            return hashlib.md5(text.encode('utf-8')).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(text.encode('utf-8')).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(text.encode('utf-8')).hexdigest()
        else:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")

    def get_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键 - 使用两个文本按顺序拼接的hash"""
        # 将两个文本按顺序拼接，然后计算hash
        combined_text = f"{text1}|||{text2}|||{self.model_name}"
        return self.get_text_hash(combined_text, 'sha256')  # 使用SHA-256替代MD5

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        if not self.use_sqlite:
            return {"error": "Statistics only available for SQLite cache"}

        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 基本统计
            cursor.execute("SELECT COUNT(*) FROM nli_cache")
            total_entries = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT model_name) FROM nli_cache")
            unique_models = cursor.fetchone()[0]

            # 文本长度统计
            cursor.execute("""
                SELECT
                    AVG(text1_length) as avg_text1_len,
                    AVG(text2_length) as avg_text2_len,
                    MAX(text1_length) as max_text1_len,
                    MAX(text2_length) as max_text2_len,
                    MIN(text1_length) as min_text1_len,
                    MIN(text2_length) as min_text2_len
                FROM nli_cache
            """)
            length_stats = cursor.fetchone()

            # 模型分布
            cursor.execute("SELECT model_name, COUNT(*) FROM nli_cache GROUP BY model_name")
            model_distribution = dict(cursor.fetchall())

            conn.close()

            return {
                "total_entries": total_entries,
                "unique_models": unique_models,
                "model_distribution": model_distribution,
                "text_length_stats": {
                    "text1": {
                        "avg": round(length_stats[0] or 0, 2),
                        "max": length_stats[2] or 0,
                        "min": length_stats[4] or 0
                    },
                    "text2": {
                        "avg": round(length_stats[1] or 0, 2),
                        "max": length_stats[3] or 0,
                        "min": length_stats[5] or 0
                    }
                }
            }

        except Exception as e:
            return {"error": f"Failed to get cache stats: {e}"}


class CachedNLICalculator(NLICalculator):
    """带缓存的NLI计算器 - 支持CSV文件持久化缓存"""

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False, use_sqlite: bool = True):
        super().__init__(model_name, verbose)

        # 缓存配置
        self.cache_dir = "cache"
        self.use_sqlite = use_sqlite

        if use_sqlite:
            self.db_file = os.path.join(self.cache_dir, "nli_cache.db")
        else:
            self.csv_cache_file = os.path.join(self.cache_dir, "nli_results_cache.csv")

        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)

        # 初始化缓存后端
        if use_sqlite:
            self._init_sqlite_cache()
        else:
            # CSV缓存不需要特殊初始化
            pass
        
    def _load_csv_cache_for_key(self, cache_key: str) -> dict:
        """为特定key从CSV文件加载缓存条目"""
        if not os.path.exists(self.csv_cache_file):
            return None

        try:
            # 读取CSV文件，使用cache_key作为索引
            df = pd.read_csv(self.csv_cache_file)

            # 如果有cache_key列，直接查找
            if 'cache_key' in df.columns:
                matching_rows = df[df['cache_key'] == cache_key]

                if len(matching_rows) > 0:
                    row = matching_rows.iloc[0]  # 取第一个匹配的行
                    return {
                        'text1': row['text1'],
                        'text2': row['text2'],
                        'model_name': row['model_name'],
                        'entailment': float(row['entailment']),
                        'neutral': float(row['neutral']),
                        'contradiction': float(row['contradiction']),
                        'cache_key': row['cache_key'],
                        'timestamp': row.get('timestamp', '')
                    }
            else:
                # 向后兼容：尝试使用旧的hash方式查找
                if self.verbose:
                    logger.debug("CSV file doesn't have cache_key column, using legacy lookup")

        except Exception as e:
            if self.verbose:
                logger.warning(f"Error loading cache for key {cache_key}: {e}")

        return None

    def _init_sqlite_cache(self):
        """初始化SQLite缓存数据库"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 检查是否存在旧版本的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='nli_cache'")
            table_exists = cursor.fetchone() is not None

            if table_exists:
                # 检查表结构是否为旧版本（包含text1, text2字段）
                cursor.execute("PRAGMA table_info(nli_cache)")
                columns = [column[1] for column in cursor.fetchall()]
                has_text_columns = 'text1' in columns and 'text2' in columns

                if has_text_columns:
                    if self.verbose:
                        logger.info("Found legacy NLI cache table, performing migration...")
                    self._migrate_legacy_cache(conn)
                else:
                    if self.verbose:
                        logger.info("Found optimized NLI cache table, no migration needed")
            else:
                # 创建新的优化缓存表
                self._create_optimized_cache_table(conn)

            conn.close()

            if self.verbose:
                logger.info(f"Initialized SQLite NLI cache: {self.db_file}")

        except Exception as e:
            logger.error(f"Failed to initialize SQLite cache: {e}")

    def _create_optimized_cache_table(self, conn):
        """创建优化的缓存表结构"""
        cursor = conn.cursor()

        # 创建优化的缓存表，不存储原始文本
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS nli_cache (
                cache_key TEXT PRIMARY KEY,
                text1_hash TEXT NOT NULL,
                text2_hash TEXT NOT NULL,
                text1_length INTEGER NOT NULL,
                text2_length INTEGER NOT NULL,
                model_name TEXT NOT NULL,
                entailment REAL NOT NULL,
                neutral REAL NOT NULL,
                contradiction REAL NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')

        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_name ON nli_cache(model_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON nli_cache(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_text_hashes ON nli_cache(text1_hash, text2_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_text_lengths ON nli_cache(text1_length, text2_length)')

        conn.commit()

        if self.verbose:
            logger.info("Created optimized NLI cache table structure")

    def _migrate_legacy_cache(self, conn):
        """迁移旧版本的缓存数据到新结构"""
        cursor = conn.cursor()

        try:
            # 创建新表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS nli_cache_new (
                    cache_key TEXT PRIMARY KEY,
                    text1_hash TEXT NOT NULL,
                    text2_hash TEXT NOT NULL,
                    text1_length INTEGER NOT NULL,
                    text2_length INTEGER NOT NULL,
                    model_name TEXT NOT NULL,
                    entailment REAL NOT NULL,
                    neutral REAL NOT NULL,
                    contradiction REAL NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')

            # 迁移数据
            cursor.execute("SELECT * FROM nli_cache")
            old_records = cursor.fetchall()

            migrated_count = 0
            for record in old_records:
                cache_key, text1, text2, model_name, entailment, neutral, contradiction, timestamp = record

                # 计算hash和长度
                text1_hash = self.get_text_hash(text1)
                text2_hash = self.get_text_hash(text2)
                text1_length = len(text1)
                text2_length = len(text2)

                # 插入到新表
                cursor.execute('''
                    INSERT OR REPLACE INTO nli_cache_new
                    (cache_key, text1_hash, text2_hash, text1_length, text2_length,
                     model_name, entailment, neutral, contradiction, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (cache_key, text1_hash, text2_hash, text1_length, text2_length,
                      model_name, entailment, neutral, contradiction, timestamp))

                migrated_count += 1

            # 删除旧表，重命名新表
            cursor.execute("DROP TABLE nli_cache")
            cursor.execute("ALTER TABLE nli_cache_new RENAME TO nli_cache")

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_name ON nli_cache(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON nli_cache(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_text_hashes ON nli_cache(text1_hash, text2_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_text_lengths ON nli_cache(text1_length, text2_length)')

            conn.commit()

            if self.verbose:
                logger.info(f"Successfully migrated {migrated_count} cache entries to optimized format")

        except Exception as e:
            # 回滚操作
            conn.rollback()
            cursor.execute("DROP TABLE IF EXISTS nli_cache_new")
            conn.commit()
            logger.error(f"Failed to migrate legacy cache: {e}")
            raise

    def _load_sqlite_cache_for_key(self, cache_key: str) -> dict:
        """从SQLite数据库加载缓存条目（优化版本）"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 首先尝试从优化表结构读取
            cursor.execute('''
                SELECT text1_hash, text2_hash, text1_length, text2_length,
                       model_name, entailment, neutral, contradiction, timestamp
                FROM nli_cache WHERE cache_key = ?
            ''', (cache_key,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return {
                    'text1_hash': row[0],
                    'text2_hash': row[1],
                    'text1_length': row[2],
                    'text2_length': row[3],
                    'model_name': row[4],
                    'entailment': float(row[5]),
                    'neutral': float(row[6]),
                    'contradiction': float(row[7]),
                    'cache_key': cache_key,
                    'timestamp': row[8]
                }

        except Exception as e:
            if self.verbose:
                logger.warning(f"Error loading SQLite cache for key {cache_key}: {e}")

        return None

    def _save_to_sqlite_cache(self, cache_key: str, data: dict):
        """保存条目到SQLite数据库（优化版本，不存储原始文本）"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 计算文本hash和长度
            text1_hash = self.get_text_hash(data['text1'])
            text2_hash = self.get_text_hash(data['text2'])
            text1_length = len(data['text1'])
            text2_length = len(data['text2'])

            # 使用INSERT OR REPLACE确保不会有重复条目
            cursor.execute('''
                INSERT OR REPLACE INTO nli_cache
                (cache_key, text1_hash, text2_hash, text1_length, text2_length,
                 model_name, entailment, neutral, contradiction, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                cache_key,
                text1_hash,
                text2_hash,
                text1_length,
                text2_length,
                data['model_name'],
                data['entailment'],
                data['neutral'],
                data['contradiction'],
                data['timestamp']
            ))

            conn.commit()
            conn.close()

            if self.verbose:
                logger.debug(f"Saved optimized NLI cache entry to SQLite: {cache_key} "
                           f"(text lengths: {text1_length}, {text2_length})")

        except Exception as e:
            logger.warning(f"Failed to save to SQLite cache: {e}")

    def _append_to_csv_cache(self, cache_key: str, data: dict):
        """将新的缓存条目追加到CSV文件"""
        try:
            # 准备新行数据，包含cache_key作为主索引
            new_row = {
                'cache_key': cache_key,
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'entailment': data['entailment'],
                'neutral': data['neutral'],
                'contradiction': data['contradiction'],
                'timestamp': data['timestamp']
            }

            # 如果文件不存在，创建新文件
            if not os.path.exists(self.csv_cache_file):
                df = pd.DataFrame([new_row])
                df.to_csv(self.csv_cache_file, index=False, encoding='utf-8-sig')
                if self.verbose:
                    logger.info(f"Created new NLI CSV cache file: {self.csv_cache_file}")
            else:
                # 检查文件是否有正确的列结构
                try:
                    existing_df = pd.read_csv(self.csv_cache_file, nrows=1)
                    if 'cache_key' not in existing_df.columns:
                        # 需要重建文件结构
                        self._rebuild_csv_with_cache_key()
                except:
                    pass

                # 追加到现有文件
                df = pd.DataFrame([new_row])
                df.to_csv(self.csv_cache_file, mode='a', header=False, index=False, encoding='utf-8-sig')

            if self.verbose:
                logger.debug(f"Appended new NLI cache entry to {self.csv_cache_file}")

        except Exception as e:
            logger.warning(f"Failed to append to NLI CSV cache: {e}")

    def _rebuild_csv_with_cache_key(self):
        """重建CSV文件，添加cache_key列作为索引"""
        try:
            if not os.path.exists(self.csv_cache_file):
                return

            # 读取现有数据
            df = pd.read_csv(self.csv_cache_file)

            # 为每行生成cache_key
            cache_keys = []
            for _, row in df.iterrows():
                combined_text = f"{row['text1']}|||{row['text2']}|||{row['model_name']}"
                cache_key = self.get_text_hash(combined_text)
                cache_keys.append(cache_key)

            # 添加cache_key列到第一列
            df.insert(0, 'cache_key', cache_keys)

            # 重新保存文件
            df.to_csv(self.csv_cache_file, index=False, encoding='utf-8-sig')

            if self.verbose:
                logger.info(f"Rebuilt CSV cache file with cache_key index: {len(df)} entries")

        except Exception as e:
            logger.warning(f"Failed to rebuild CSV cache file: {e}")

    def compute_nli_scores_cached(self, text1: str, text2: str) -> NLIResult:
        """
        带缓存的NLI分数计算 - 只使用本地文件缓存

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        cache_key = self.get_cache_key(text1, text2)

        # 1. 从缓存读取（SQLite或CSV）
        if self.use_sqlite:
            cached_data = self._load_sqlite_cache_for_key(cache_key)
            cache_type = "SQLite"
        else:
            cached_data = self._load_csv_cache_for_key(cache_key)
            cache_type = "CSV"

        if cached_data:
            # 尝试获取logits，如果没有则为None（向后兼容）
            logits = cached_data.get('logits', None)
            if logits and isinstance(logits, str):
                # 如果logits是字符串，尝试解析
                try:
                    import json
                    logits = json.loads(logits)
                except:
                    logits = None

            nli_result = NLIResult(
                entailment=cached_data['entailment'],
                neutral=cached_data['neutral'],
                contradiction=cached_data['contradiction'],
                logits=logits
            )
            if self.verbose:
                logger.debug(f"Loaded NLI result from {cache_type} cache")
            return nli_result

        # 2. 计算新的NLI分数
        if self.verbose:
            logger.info(f"Computing new NLI scores for model {self.model_name}")
        nli_result = self.compute_nli_scores(text1, text2)

        # 3. 立即保存到CSV文件
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        cache_data = {
            'text1': text1,
            'text2': text2,
            'model_name': self.model_name,
            'entailment': nli_result.entailment,
            'neutral': nli_result.neutral,
            'contradiction': nli_result.contradiction,
            'logits': nli_result.logits,
            'timestamp': timestamp
        }

        # 4. 立即保存到缓存（SQLite或CSV）
        if self.use_sqlite:
            self._save_to_sqlite_cache(cache_key, cache_data)
        else:
            self._append_to_csv_cache(cache_key, cache_data)

        return nli_result
    
    def compute_similarity_matrix_cached(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """计算响应列表的缓存相似度矩阵"""
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 使用缓存计算双向分数并取平均
                    nli_ij = self.compute_nli_scores_cached(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores_cached(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        if self.use_sqlite:
            # 统计SQLite数据库中的条目数
            cache_entries = 0
            try:
                conn = sqlite3.connect(self.db_file)
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM nli_cache')
                cache_entries = cursor.fetchone()[0]
                conn.close()
            except:
                cache_entries = 0

            return {
                'cache_entries': cache_entries,
                'cache_type': 'SQLite',
                'model_name': self.model_name,
                'cache_file': self.db_file
            }
        else:
            # 统计CSV文件中的条目数
            cache_entries = 0
            if os.path.exists(self.csv_cache_file):
                try:
                    df = pd.read_csv(self.csv_cache_file)
                    cache_entries = len(df)
                except:
                    cache_entries = 0

            return {
                'cache_entries': cache_entries,
                'cache_type': 'CSV',
                'model_name': self.model_name,
                'cache_file': self.csv_cache_file
            }

    def clear_cache(self):
        """清空CSV文件缓存"""
        if os.path.exists(self.csv_cache_file):
            os.remove(self.csv_cache_file)
        if self.verbose:
            logger.info("NLI CSV cache cleared")

    def save_cache(self):
        """手动保存CSV缓存（现在每次操作都自动保存，此方法保留兼容性）"""
        if self.verbose:
            logger.info("Cache is automatically saved after each operation")

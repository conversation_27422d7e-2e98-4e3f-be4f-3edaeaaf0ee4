2025-08-15 11:49:08,710 - __main__ - INFO - Initializing UQ analysis system...
2025-08-15 11:49:09,914 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-08-15 11:49:09,914 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-08-15 11:49:10,402 - datasets - INFO - PyTorch version 2.6.0 available.
2025-08-15 11:49:10,607 - uq_analysis.method_loader - INFO - Discovered 11 UQ methods: ['EigValLaplacianNLIUQ', 'LofreeCPUQ', 'LUQUQ', 'EmbeddingE5UQ', 'SemanticEntropyNLIUQ', 'EigValLaplacianJaccardUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ', 'EmbeddingQwenUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ']
2025-08-15 11:49:10,607 - __main__ - INFO - Discovered 11 UQ methods
2025-08-15 11:49:10,608 - uq_analysis.data_processor - INFO - Connected to MongoDB: localhost:27017/LLM-UQ
2025-08-15 11:49:10,609 - __main__ - INFO - Initialization complete
2025-08-15 11:49:10,609 - uq_analysis.progress_manager - INFO - Starting UQ analysis with 1 tasks
2025-08-15 11:49:10,609 - __main__ - INFO - Processing task: counterfactual_qa
2025-08-15 11:49:13,261 - uq_analysis.method_loader - INFO - Successfully loaded 1 methods: ['LUQUQ']
2025-08-15 11:49:13,261 - __main__ - INFO - Processing counterfactual_qa/counterfactual_data
2025-08-15 11:49:13,299 - uq_analysis.data_processor - INFO - Found 10 groups for counterfactual_qa/counterfactual_data
2025-08-15 11:49:13,300 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_LUQ_counterfactual...
2025-08-15 11:49:13,300 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_LUQ_counterfactual
2025-08-15 11:49:13,300 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 10 pending, 10 total
2025-08-15 11:49:13,301 - uq_analysis.progress_manager - INFO - Starting task counterfactual_qa/counterfactual_data: 10 groups, 1 methods each
2025-08-15 11:49:13,301 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:13,305 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:13,305 - uq_methods.implementations.luq - INFO - Split text (5590 chars) into 43 sentences
2025-08-15 11:49:13,305 - uq_methods.implementations.luq - INFO - Response 1 split into 43 sentences
2025-08-15 11:49:13,307 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:13,307 - uq_methods.implementations.luq - INFO - Split text (4648 chars) into 40 sentences
2025-08-15 11:49:13,307 - uq_methods.implementations.luq - INFO - Response 2 split into 40 sentences
2025-08-15 11:49:13,310 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 32 sentences
2025-08-15 11:49:13,310 - uq_methods.implementations.luq - INFO - Split text (5184 chars) into 32 sentences
2025-08-15 11:49:13,310 - uq_methods.implementations.luq - INFO - Response 3 split into 32 sentences
2025-08-15 11:49:13,312 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:13,312 - uq_methods.implementations.luq - INFO - Split text (5739 chars) into 42 sentences
2025-08-15 11:49:13,312 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:49:13,314 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:49:13,314 - uq_methods.implementations.luq - INFO - Split text (5019 chars) into 33 sentences
2025-08-15 11:49:13,314 - uq_methods.implementations.luq - INFO - Response 5 split into 33 sentences
2025-08-15 11:49:13,317 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:13,317 - uq_methods.implementations.luq - INFO - Split text (5354 chars) into 36 sentences
2025-08-15 11:49:13,317 - uq_methods.implementations.luq - INFO - Response 6 split into 36 sentences
2025-08-15 11:49:13,319 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:13,319 - uq_methods.implementations.luq - INFO - Split text (5809 chars) into 35 sentences
2025-08-15 11:49:13,319 - uq_methods.implementations.luq - INFO - Response 7 split into 35 sentences
2025-08-15 11:49:13,323 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:13,323 - uq_methods.implementations.luq - INFO - Split text (6877 chars) into 41 sentences
2025-08-15 11:49:13,323 - uq_methods.implementations.luq - INFO - Response 8 split into 41 sentences
2025-08-15 11:49:13,325 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:13,325 - uq_methods.implementations.luq - INFO - Split text (7039 chars) into 42 sentences
2025-08-15 11:49:13,325 - uq_methods.implementations.luq - INFO - Response 9 split into 42 sentences
2025-08-15 11:49:13,329 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:13,329 - uq_methods.implementations.luq - INFO - Split text (7868 chars) into 49 sentences
2025-08-15 11:49:13,329 - uq_methods.implementations.luq - INFO - Response 10 split into 49 sentences
2025-08-15 11:49:13,331 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:49:13,331 - uq_methods.implementations.luq - INFO - Split text (4593 chars) into 25 sentences
2025-08-15 11:49:13,331 - uq_methods.implementations.luq - INFO - Response 11 split into 25 sentences
2025-08-15 11:49:13,333 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:13,333 - uq_methods.implementations.luq - INFO - Split text (5815 chars) into 37 sentences
2025-08-15 11:49:13,333 - uq_methods.implementations.luq - INFO - Response 12 split into 37 sentences
2025-08-15 11:49:13,336 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:13,336 - uq_methods.implementations.luq - INFO - Split text (6594 chars) into 40 sentences
2025-08-15 11:49:13,336 - uq_methods.implementations.luq - INFO - Response 13 split into 40 sentences
2025-08-15 11:49:13,338 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:49:13,338 - uq_methods.implementations.luq - INFO - Split text (5683 chars) into 30 sentences
2025-08-15 11:49:13,338 - uq_methods.implementations.luq - INFO - Response 14 split into 30 sentences
2025-08-15 11:49:13,341 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:13,341 - uq_methods.implementations.luq - INFO - Split text (6747 chars) into 44 sentences
2025-08-15 11:49:13,341 - uq_methods.implementations.luq - INFO - Response 15 split into 44 sentences
2025-08-15 11:49:13,344 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:13,344 - uq_methods.implementations.luq - INFO - Split text (7245 chars) into 46 sentences
2025-08-15 11:49:13,344 - uq_methods.implementations.luq - INFO - Response 16 split into 46 sentences
2025-08-15 11:49:13,347 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:13,347 - uq_methods.implementations.luq - INFO - Split text (7117 chars) into 43 sentences
2025-08-15 11:49:13,347 - uq_methods.implementations.luq - INFO - Response 17 split into 43 sentences
2025-08-15 11:49:13,350 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:13,350 - uq_methods.implementations.luq - INFO - Split text (5423 chars) into 38 sentences
2025-08-15 11:49:13,350 - uq_methods.implementations.luq - INFO - Response 18 split into 38 sentences
2025-08-15 11:49:13,352 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:13,352 - uq_methods.implementations.luq - INFO - Split text (6157 chars) into 37 sentences
2025-08-15 11:49:13,352 - uq_methods.implementations.luq - INFO - Response 19 split into 37 sentences
2025-08-15 11:49:13,355 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:13,355 - uq_methods.implementations.luq - INFO - Split text (5647 chars) into 41 sentences
2025-08-15 11:49:13,355 - uq_methods.implementations.luq - INFO - Response 20 split into 41 sentences
2025-08-15 11:49:13,357 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:13,357 - uq_methods.implementations.luq - INFO - Split text (5527 chars) into 36 sentences
2025-08-15 11:49:13,357 - uq_methods.implementations.luq - INFO - Response 21 split into 36 sentences
2025-08-15 11:49:13,360 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:13,360 - uq_methods.implementations.luq - INFO - Split text (6011 chars) into 42 sentences
2025-08-15 11:49:13,360 - uq_methods.implementations.luq - INFO - Response 22 split into 42 sentences
2025-08-15 11:49:13,362 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:13,362 - uq_methods.implementations.luq - INFO - Split text (5658 chars) into 40 sentences
2025-08-15 11:49:13,363 - uq_methods.implementations.luq - INFO - Response 23 split into 40 sentences
2025-08-15 11:49:13,365 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:13,365 - uq_methods.implementations.luq - INFO - Split text (5395 chars) into 38 sentences
2025-08-15 11:49:13,365 - uq_methods.implementations.luq - INFO - Response 24 split into 38 sentences
2025-08-15 11:49:13,368 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:13,368 - uq_methods.implementations.luq - INFO - Split text (6789 chars) into 50 sentences
2025-08-15 11:49:13,368 - uq_methods.implementations.luq - INFO - Response 25 split into 50 sentences
2025-08-15 11:49:13,371 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 59 sentences
2025-08-15 11:49:13,371 - uq_methods.implementations.luq - INFO - Split text (7909 chars) into 59 sentences
2025-08-15 11:49:13,371 - uq_methods.implementations.luq - INFO - Response 26 split into 59 sentences
2025-08-15 11:49:13,374 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:13,374 - uq_methods.implementations.luq - INFO - Split text (6581 chars) into 45 sentences
2025-08-15 11:49:13,374 - uq_methods.implementations.luq - INFO - Response 27 split into 45 sentences
2025-08-15 11:49:13,377 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:13,377 - uq_methods.implementations.luq - INFO - Split text (6876 chars) into 48 sentences
2025-08-15 11:49:13,377 - uq_methods.implementations.luq - INFO - Response 28 split into 48 sentences
2025-08-15 11:49:13,380 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:13,380 - uq_methods.implementations.luq - INFO - Split text (6753 chars) into 48 sentences
2025-08-15 11:49:13,380 - uq_methods.implementations.luq - INFO - Response 29 split into 48 sentences
2025-08-15 11:49:13,382 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:13,382 - uq_methods.implementations.luq - INFO - Split text (5923 chars) into 46 sentences
2025-08-15 11:49:13,382 - uq_methods.implementations.luq - INFO - Response 30 split into 46 sentences
2025-08-15 11:49:13,382 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:13,484 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:13,577 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:49:13,652 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:49:13,750 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:49:13,828 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:49:13,912 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:49:13,994 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:49:14,089 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:49:14,186 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:49:14,300 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:49:14,357 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:49:14,443 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:49:14,537 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:49:14,606 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:49:14,709 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:49:14,816 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:49:14,915 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:49:15,003 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:49:15,089 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:49:15,187 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:49:15,270 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:49:15,368 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:49:15,460 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:49:15,548 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:49:15,664 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:49:15,800 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:49:15,905 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:49:16,016 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:49:16,127 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:49:16,286 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:16,286 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=4, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:16,286 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:16,288 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:16,288 - uq_methods.implementations.luq - INFO - Split text (5398 chars) into 40 sentences
2025-08-15 11:49:16,288 - uq_methods.implementations.luq - INFO - Response 1 split into 40 sentences
2025-08-15 11:49:16,291 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:16,291 - uq_methods.implementations.luq - INFO - Split text (5067 chars) into 39 sentences
2025-08-15 11:49:16,291 - uq_methods.implementations.luq - INFO - Response 2 split into 39 sentences
2025-08-15 11:49:16,293 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:49:16,293 - uq_methods.implementations.luq - INFO - Split text (4580 chars) into 33 sentences
2025-08-15 11:49:16,293 - uq_methods.implementations.luq - INFO - Response 3 split into 33 sentences
2025-08-15 11:49:16,295 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:16,295 - uq_methods.implementations.luq - INFO - Split text (6027 chars) into 42 sentences
2025-08-15 11:49:16,295 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:49:16,297 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:16,298 - uq_methods.implementations.luq - INFO - Split text (4980 chars) into 35 sentences
2025-08-15 11:49:16,298 - uq_methods.implementations.luq - INFO - Response 5 split into 35 sentences
2025-08-15 11:49:16,300 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:16,300 - uq_methods.implementations.luq - INFO - Split text (5575 chars) into 41 sentences
2025-08-15 11:49:16,300 - uq_methods.implementations.luq - INFO - Response 6 split into 41 sentences
2025-08-15 11:49:16,302 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:16,302 - uq_methods.implementations.luq - INFO - Split text (6138 chars) into 43 sentences
2025-08-15 11:49:16,302 - uq_methods.implementations.luq - INFO - Response 7 split into 43 sentences
2025-08-15 11:49:16,305 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:16,305 - uq_methods.implementations.luq - INFO - Split text (6654 chars) into 49 sentences
2025-08-15 11:49:16,305 - uq_methods.implementations.luq - INFO - Response 8 split into 49 sentences
2025-08-15 11:49:16,308 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:16,308 - uq_methods.implementations.luq - INFO - Split text (6597 chars) into 42 sentences
2025-08-15 11:49:16,308 - uq_methods.implementations.luq - INFO - Response 9 split into 42 sentences
2025-08-15 11:49:16,311 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:16,311 - uq_methods.implementations.luq - INFO - Split text (6866 chars) into 47 sentences
2025-08-15 11:49:16,311 - uq_methods.implementations.luq - INFO - Response 10 split into 47 sentences
2025-08-15 11:49:16,314 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:16,314 - uq_methods.implementations.luq - INFO - Split text (6706 chars) into 52 sentences
2025-08-15 11:49:16,314 - uq_methods.implementations.luq - INFO - Response 11 split into 52 sentences
2025-08-15 11:49:16,317 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:16,317 - uq_methods.implementations.luq - INFO - Split text (7344 chars) into 49 sentences
2025-08-15 11:49:16,317 - uq_methods.implementations.luq - INFO - Response 12 split into 49 sentences
2025-08-15 11:49:16,319 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:16,319 - uq_methods.implementations.luq - INFO - Split text (5029 chars) into 35 sentences
2025-08-15 11:49:16,319 - uq_methods.implementations.luq - INFO - Response 13 split into 35 sentences
2025-08-15 11:49:16,322 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:16,322 - uq_methods.implementations.luq - INFO - Split text (6388 chars) into 40 sentences
2025-08-15 11:49:16,322 - uq_methods.implementations.luq - INFO - Response 14 split into 40 sentences
2025-08-15 11:49:16,324 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:16,324 - uq_methods.implementations.luq - INFO - Split text (6336 chars) into 41 sentences
2025-08-15 11:49:16,324 - uq_methods.implementations.luq - INFO - Response 15 split into 41 sentences
2025-08-15 11:49:16,326 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:16,326 - uq_methods.implementations.luq - INFO - Split text (4878 chars) into 36 sentences
2025-08-15 11:49:16,326 - uq_methods.implementations.luq - INFO - Response 16 split into 36 sentences
2025-08-15 11:49:16,329 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:16,329 - uq_methods.implementations.luq - INFO - Split text (6130 chars) into 45 sentences
2025-08-15 11:49:16,329 - uq_methods.implementations.luq - INFO - Response 17 split into 45 sentences
2025-08-15 11:49:16,331 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:16,331 - uq_methods.implementations.luq - INFO - Split text (5738 chars) into 37 sentences
2025-08-15 11:49:16,331 - uq_methods.implementations.luq - INFO - Response 18 split into 37 sentences
2025-08-15 11:49:16,334 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:16,334 - uq_methods.implementations.luq - INFO - Split text (6304 chars) into 46 sentences
2025-08-15 11:49:16,334 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:49:16,337 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:16,337 - uq_methods.implementations.luq - INFO - Split text (6156 chars) into 43 sentences
2025-08-15 11:49:16,337 - uq_methods.implementations.luq - INFO - Response 20 split into 43 sentences
2025-08-15 11:49:16,339 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:16,339 - uq_methods.implementations.luq - INFO - Split text (6170 chars) into 48 sentences
2025-08-15 11:49:16,339 - uq_methods.implementations.luq - INFO - Response 21 split into 48 sentences
2025-08-15 11:49:16,342 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:16,342 - uq_methods.implementations.luq - INFO - Split text (6138 chars) into 40 sentences
2025-08-15 11:49:16,342 - uq_methods.implementations.luq - INFO - Response 22 split into 40 sentences
2025-08-15 11:49:16,345 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:16,345 - uq_methods.implementations.luq - INFO - Split text (6625 chars) into 45 sentences
2025-08-15 11:49:16,345 - uq_methods.implementations.luq - INFO - Response 23 split into 45 sentences
2025-08-15 11:49:16,347 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:16,347 - uq_methods.implementations.luq - INFO - Split text (6489 chars) into 41 sentences
2025-08-15 11:49:16,347 - uq_methods.implementations.luq - INFO - Response 24 split into 41 sentences
2025-08-15 11:49:16,349 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 28 sentences
2025-08-15 11:49:16,349 - uq_methods.implementations.luq - INFO - Split text (4715 chars) into 28 sentences
2025-08-15 11:49:16,349 - uq_methods.implementations.luq - INFO - Response 25 split into 28 sentences
2025-08-15 11:49:16,352 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:16,352 - uq_methods.implementations.luq - INFO - Split text (5319 chars) into 37 sentences
2025-08-15 11:49:16,352 - uq_methods.implementations.luq - INFO - Response 26 split into 37 sentences
2025-08-15 11:49:16,354 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:16,354 - uq_methods.implementations.luq - INFO - Split text (5178 chars) into 35 sentences
2025-08-15 11:49:16,354 - uq_methods.implementations.luq - INFO - Response 27 split into 35 sentences
2025-08-15 11:49:16,356 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:16,356 - uq_methods.implementations.luq - INFO - Split text (4807 chars) into 35 sentences
2025-08-15 11:49:16,356 - uq_methods.implementations.luq - INFO - Response 28 split into 35 sentences
2025-08-15 11:49:16,358 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:49:16,358 - uq_methods.implementations.luq - INFO - Split text (5120 chars) into 33 sentences
2025-08-15 11:49:16,358 - uq_methods.implementations.luq - INFO - Response 29 split into 33 sentences
2025-08-15 11:49:16,360 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:16,360 - uq_methods.implementations.luq - INFO - Split text (5051 chars) into 36 sentences
2025-08-15 11:49:16,360 - uq_methods.implementations.luq - INFO - Response 30 split into 36 sentences
2025-08-15 11:49:16,360 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:16,455 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:16,545 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:49:16,621 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:49:16,719 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:49:16,799 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:49:16,894 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:49:16,992 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:49:17,105 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:49:17,201 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:49:17,309 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:49:17,429 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:49:17,542 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:49:17,623 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:49:17,715 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:49:17,944 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:49:18,028 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:49:18,131 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:49:18,217 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:49:18,323 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:49:18,421 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:49:18,533 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:49:18,625 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:49:18,728 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:49:18,823 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:49:18,888 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:49:18,974 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:49:19,054 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:49:19,135 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:49:19,211 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:49:19,341 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:19,341 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=2, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:19,341 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:19,344 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:19,344 - uq_methods.implementations.luq - INFO - Split text (4949 chars) into 43 sentences
2025-08-15 11:49:19,344 - uq_methods.implementations.luq - INFO - Response 1 split into 43 sentences
2025-08-15 11:49:19,346 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:19,346 - uq_methods.implementations.luq - INFO - Split text (4995 chars) into 41 sentences
2025-08-15 11:49:19,346 - uq_methods.implementations.luq - INFO - Response 2 split into 41 sentences
2025-08-15 11:49:19,349 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:19,349 - uq_methods.implementations.luq - INFO - Split text (6059 chars) into 51 sentences
2025-08-15 11:49:19,349 - uq_methods.implementations.luq - INFO - Response 3 split into 51 sentences
2025-08-15 11:49:19,351 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:19,351 - uq_methods.implementations.luq - INFO - Split text (5368 chars) into 35 sentences
2025-08-15 11:49:19,351 - uq_methods.implementations.luq - INFO - Response 4 split into 35 sentences
2025-08-15 11:49:19,354 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:19,354 - uq_methods.implementations.luq - INFO - Split text (5116 chars) into 37 sentences
2025-08-15 11:49:19,354 - uq_methods.implementations.luq - INFO - Response 5 split into 37 sentences
2025-08-15 11:49:19,356 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:19,356 - uq_methods.implementations.luq - INFO - Split text (4997 chars) into 41 sentences
2025-08-15 11:49:19,356 - uq_methods.implementations.luq - INFO - Response 6 split into 41 sentences
2025-08-15 11:49:19,359 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:19,359 - uq_methods.implementations.luq - INFO - Split text (6277 chars) into 42 sentences
2025-08-15 11:49:19,359 - uq_methods.implementations.luq - INFO - Response 7 split into 42 sentences
2025-08-15 11:49:19,362 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:19,362 - uq_methods.implementations.luq - INFO - Split text (6459 chars) into 47 sentences
2025-08-15 11:49:19,362 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:49:19,364 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:19,364 - uq_methods.implementations.luq - INFO - Split text (6549 chars) into 46 sentences
2025-08-15 11:49:19,365 - uq_methods.implementations.luq - INFO - Response 9 split into 46 sentences
2025-08-15 11:49:19,367 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:19,367 - uq_methods.implementations.luq - INFO - Split text (6125 chars) into 49 sentences
2025-08-15 11:49:19,367 - uq_methods.implementations.luq - INFO - Response 10 split into 49 sentences
2025-08-15 11:49:19,370 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:19,370 - uq_methods.implementations.luq - INFO - Split text (6866 chars) into 51 sentences
2025-08-15 11:49:19,370 - uq_methods.implementations.luq - INFO - Response 11 split into 51 sentences
2025-08-15 11:49:19,373 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:19,373 - uq_methods.implementations.luq - INFO - Split text (6826 chars) into 46 sentences
2025-08-15 11:49:19,373 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:49:19,376 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:19,376 - uq_methods.implementations.luq - INFO - Split text (6402 chars) into 51 sentences
2025-08-15 11:49:19,376 - uq_methods.implementations.luq - INFO - Response 13 split into 51 sentences
2025-08-15 11:49:19,379 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:49:19,379 - uq_methods.implementations.luq - INFO - Split text (7118 chars) into 53 sentences
2025-08-15 11:49:19,379 - uq_methods.implementations.luq - INFO - Response 14 split into 53 sentences
2025-08-15 11:49:19,382 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:19,382 - uq_methods.implementations.luq - INFO - Split text (6824 chars) into 52 sentences
2025-08-15 11:49:19,382 - uq_methods.implementations.luq - INFO - Response 15 split into 52 sentences
2025-08-15 11:49:19,385 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:19,385 - uq_methods.implementations.luq - INFO - Split text (6531 chars) into 51 sentences
2025-08-15 11:49:19,385 - uq_methods.implementations.luq - INFO - Response 16 split into 51 sentences
2025-08-15 11:49:19,388 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:19,388 - uq_methods.implementations.luq - INFO - Split text (6962 chars) into 49 sentences
2025-08-15 11:49:19,388 - uq_methods.implementations.luq - INFO - Response 17 split into 49 sentences
2025-08-15 11:49:19,391 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:49:19,391 - uq_methods.implementations.luq - INFO - Split text (6769 chars) into 56 sentences
2025-08-15 11:49:19,391 - uq_methods.implementations.luq - INFO - Response 18 split into 56 sentences
2025-08-15 11:49:19,393 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:19,393 - uq_methods.implementations.luq - INFO - Split text (5366 chars) into 43 sentences
2025-08-15 11:49:19,393 - uq_methods.implementations.luq - INFO - Response 19 split into 43 sentences
2025-08-15 11:49:19,396 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 64 sentences
2025-08-15 11:49:19,397 - uq_methods.implementations.luq - INFO - Split text (7023 chars) into 64 sentences
2025-08-15 11:49:19,397 - uq_methods.implementations.luq - INFO - Response 20 split into 64 sentences
2025-08-15 11:49:19,399 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:19,399 - uq_methods.implementations.luq - INFO - Split text (5725 chars) into 51 sentences
2025-08-15 11:49:19,399 - uq_methods.implementations.luq - INFO - Response 21 split into 51 sentences
2025-08-15 11:49:19,401 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:19,401 - uq_methods.implementations.luq - INFO - Split text (5140 chars) into 37 sentences
2025-08-15 11:49:19,401 - uq_methods.implementations.luq - INFO - Response 22 split into 37 sentences
2025-08-15 11:49:19,404 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:19,404 - uq_methods.implementations.luq - INFO - Split text (5671 chars) into 50 sentences
2025-08-15 11:49:19,404 - uq_methods.implementations.luq - INFO - Response 23 split into 50 sentences
2025-08-15 11:49:19,407 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:19,407 - uq_methods.implementations.luq - INFO - Split text (6323 chars) into 46 sentences
2025-08-15 11:49:19,407 - uq_methods.implementations.luq - INFO - Response 24 split into 46 sentences
2025-08-15 11:49:19,409 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:19,409 - uq_methods.implementations.luq - INFO - Split text (5368 chars) into 37 sentences
2025-08-15 11:49:19,409 - uq_methods.implementations.luq - INFO - Response 25 split into 37 sentences
2025-08-15 11:49:19,412 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:19,412 - uq_methods.implementations.luq - INFO - Split text (6149 chars) into 42 sentences
2025-08-15 11:49:19,412 - uq_methods.implementations.luq - INFO - Response 26 split into 42 sentences
2025-08-15 11:49:19,415 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:19,415 - uq_methods.implementations.luq - INFO - Split text (7076 chars) into 47 sentences
2025-08-15 11:49:19,415 - uq_methods.implementations.luq - INFO - Response 27 split into 47 sentences
2025-08-15 11:49:19,418 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:19,418 - uq_methods.implementations.luq - INFO - Split text (6945 chars) into 43 sentences
2025-08-15 11:49:19,418 - uq_methods.implementations.luq - INFO - Response 28 split into 43 sentences
2025-08-15 11:49:19,421 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:19,421 - uq_methods.implementations.luq - INFO - Split text (6660 chars) into 50 sentences
2025-08-15 11:49:19,421 - uq_methods.implementations.luq - INFO - Response 29 split into 50 sentences
2025-08-15 11:49:19,423 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:19,423 - uq_methods.implementations.luq - INFO - Split text (6045 chars) into 43 sentences
2025-08-15 11:49:19,423 - uq_methods.implementations.luq - INFO - Response 30 split into 43 sentences
2025-08-15 11:49:19,424 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:19,528 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:19,624 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:49:19,742 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:49:19,823 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:49:19,910 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:49:20,005 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:49:20,104 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:49:20,213 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:49:20,320 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:49:20,434 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:49:20,552 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:49:20,661 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:49:20,780 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:49:20,904 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:49:21,025 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:49:21,145 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:49:21,260 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:49:21,391 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:49:21,492 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:49:21,641 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:49:21,760 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:49:21,847 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:49:21,963 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:49:22,071 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:49:22,159 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:49:22,257 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:49:22,367 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:49:22,468 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:49:22,585 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:49:22,740 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:22,740 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=3, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:22,740 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:22,743 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:22,743 - uq_methods.implementations.luq - INFO - Split text (4893 chars) into 35 sentences
2025-08-15 11:49:22,743 - uq_methods.implementations.luq - INFO - Response 1 split into 35 sentences
2025-08-15 11:49:22,745 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:49:22,745 - uq_methods.implementations.luq - INFO - Split text (4087 chars) into 31 sentences
2025-08-15 11:49:22,745 - uq_methods.implementations.luq - INFO - Response 2 split into 31 sentences
2025-08-15 11:49:22,747 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:22,747 - uq_methods.implementations.luq - INFO - Split text (5044 chars) into 37 sentences
2025-08-15 11:49:22,747 - uq_methods.implementations.luq - INFO - Response 3 split into 37 sentences
2025-08-15 11:49:22,749 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:49:22,749 - uq_methods.implementations.luq - INFO - Split text (4308 chars) into 30 sentences
2025-08-15 11:49:22,749 - uq_methods.implementations.luq - INFO - Response 4 split into 30 sentences
2025-08-15 11:49:22,751 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:22,751 - uq_methods.implementations.luq - INFO - Split text (4822 chars) into 37 sentences
2025-08-15 11:49:22,751 - uq_methods.implementations.luq - INFO - Response 5 split into 37 sentences
2025-08-15 11:49:22,753 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:22,753 - uq_methods.implementations.luq - INFO - Split text (5108 chars) into 36 sentences
2025-08-15 11:49:22,753 - uq_methods.implementations.luq - INFO - Response 6 split into 36 sentences
2025-08-15 11:49:22,756 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:22,756 - uq_methods.implementations.luq - INFO - Split text (6156 chars) into 41 sentences
2025-08-15 11:49:22,756 - uq_methods.implementations.luq - INFO - Response 7 split into 41 sentences
2025-08-15 11:49:22,759 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:22,759 - uq_methods.implementations.luq - INFO - Split text (5870 chars) into 47 sentences
2025-08-15 11:49:22,759 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:49:22,761 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:22,761 - uq_methods.implementations.luq - INFO - Split text (6435 chars) into 44 sentences
2025-08-15 11:49:22,761 - uq_methods.implementations.luq - INFO - Response 9 split into 44 sentences
2025-08-15 11:49:22,764 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:22,764 - uq_methods.implementations.luq - INFO - Split text (5596 chars) into 41 sentences
2025-08-15 11:49:22,764 - uq_methods.implementations.luq - INFO - Response 10 split into 41 sentences
2025-08-15 11:49:22,766 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:22,766 - uq_methods.implementations.luq - INFO - Split text (5196 chars) into 43 sentences
2025-08-15 11:49:22,766 - uq_methods.implementations.luq - INFO - Response 11 split into 43 sentences
2025-08-15 11:49:22,769 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:22,769 - uq_methods.implementations.luq - INFO - Split text (5944 chars) into 46 sentences
2025-08-15 11:49:22,769 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:49:22,772 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:22,772 - uq_methods.implementations.luq - INFO - Split text (5437 chars) into 37 sentences
2025-08-15 11:49:22,772 - uq_methods.implementations.luq - INFO - Response 13 split into 37 sentences
2025-08-15 11:49:22,774 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:22,774 - uq_methods.implementations.luq - INFO - Split text (5063 chars) into 37 sentences
2025-08-15 11:49:22,774 - uq_methods.implementations.luq - INFO - Response 14 split into 37 sentences
2025-08-15 11:49:22,776 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:22,776 - uq_methods.implementations.luq - INFO - Split text (5185 chars) into 40 sentences
2025-08-15 11:49:22,776 - uq_methods.implementations.luq - INFO - Response 15 split into 40 sentences
2025-08-15 11:49:22,779 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:22,779 - uq_methods.implementations.luq - INFO - Split text (5380 chars) into 39 sentences
2025-08-15 11:49:22,779 - uq_methods.implementations.luq - INFO - Response 16 split into 39 sentences
2025-08-15 11:49:22,781 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:22,781 - uq_methods.implementations.luq - INFO - Split text (5351 chars) into 36 sentences
2025-08-15 11:49:22,781 - uq_methods.implementations.luq - INFO - Response 17 split into 36 sentences
2025-08-15 11:49:22,783 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:22,783 - uq_methods.implementations.luq - INFO - Split text (5606 chars) into 44 sentences
2025-08-15 11:49:22,783 - uq_methods.implementations.luq - INFO - Response 18 split into 44 sentences
2025-08-15 11:49:22,786 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:22,786 - uq_methods.implementations.luq - INFO - Split text (6064 chars) into 44 sentences
2025-08-15 11:49:22,786 - uq_methods.implementations.luq - INFO - Response 19 split into 44 sentences
2025-08-15 11:49:22,789 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:22,789 - uq_methods.implementations.luq - INFO - Split text (6740 chars) into 51 sentences
2025-08-15 11:49:22,789 - uq_methods.implementations.luq - INFO - Response 20 split into 51 sentences
2025-08-15 11:49:22,792 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:22,792 - uq_methods.implementations.luq - INFO - Split text (5832 chars) into 43 sentences
2025-08-15 11:49:22,792 - uq_methods.implementations.luq - INFO - Response 21 split into 43 sentences
2025-08-15 11:49:22,794 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:22,794 - uq_methods.implementations.luq - INFO - Split text (5767 chars) into 41 sentences
2025-08-15 11:49:22,794 - uq_methods.implementations.luq - INFO - Response 22 split into 41 sentences
2025-08-15 11:49:22,797 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:22,797 - uq_methods.implementations.luq - INFO - Split text (6228 chars) into 49 sentences
2025-08-15 11:49:22,797 - uq_methods.implementations.luq - INFO - Response 23 split into 49 sentences
2025-08-15 11:49:22,800 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:22,800 - uq_methods.implementations.luq - INFO - Split text (6727 chars) into 49 sentences
2025-08-15 11:49:22,800 - uq_methods.implementations.luq - INFO - Response 24 split into 49 sentences
2025-08-15 11:49:22,801 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 27 sentences
2025-08-15 11:49:22,801 - uq_methods.implementations.luq - INFO - Split text (3771 chars) into 27 sentences
2025-08-15 11:49:22,802 - uq_methods.implementations.luq - INFO - Response 25 split into 27 sentences
2025-08-15 11:49:22,803 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:49:22,803 - uq_methods.implementations.luq - INFO - Split text (4275 chars) into 25 sentences
2025-08-15 11:49:22,803 - uq_methods.implementations.luq - INFO - Response 26 split into 25 sentences
2025-08-15 11:49:22,805 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:49:22,805 - uq_methods.implementations.luq - INFO - Split text (4183 chars) into 25 sentences
2025-08-15 11:49:22,805 - uq_methods.implementations.luq - INFO - Response 27 split into 25 sentences
2025-08-15 11:49:22,807 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 28 sentences
2025-08-15 11:49:22,807 - uq_methods.implementations.luq - INFO - Split text (4598 chars) into 28 sentences
2025-08-15 11:49:22,807 - uq_methods.implementations.luq - INFO - Response 28 split into 28 sentences
2025-08-15 11:49:22,809 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:49:22,809 - uq_methods.implementations.luq - INFO - Split text (4622 chars) into 31 sentences
2025-08-15 11:49:22,810 - uq_methods.implementations.luq - INFO - Response 29 split into 31 sentences
2025-08-15 11:49:22,812 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:49:22,812 - uq_methods.implementations.luq - INFO - Split text (4577 chars) into 30 sentences
2025-08-15 11:49:22,812 - uq_methods.implementations.luq - INFO - Response 30 split into 30 sentences
2025-08-15 11:49:22,812 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:22,897 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:22,972 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:49:23,061 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:49:23,134 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:49:23,223 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:49:23,310 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:49:23,408 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:49:23,522 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:49:23,628 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:49:23,727 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:49:23,831 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:49:23,942 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:49:24,031 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:49:24,121 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:49:24,219 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:49:24,313 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:49:24,400 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:49:24,506 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:49:24,612 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:49:24,735 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:49:24,839 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:49:24,938 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:49:25,057 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:49:25,176 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:49:25,242 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:49:25,304 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:49:25,365 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:49:25,433 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:49:25,507 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:49:25,627 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:25,627 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=8, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:25,627 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 44 responses
2025-08-15 11:49:25,630 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:25,630 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 42 sentences
2025-08-15 11:49:25,630 - uq_methods.implementations.luq - INFO - Response 1 split into 42 sentences
2025-08-15 11:49:25,633 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:25,633 - uq_methods.implementations.luq - INFO - Split text (5720 chars) into 46 sentences
2025-08-15 11:49:25,633 - uq_methods.implementations.luq - INFO - Response 2 split into 46 sentences
2025-08-15 11:49:25,636 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:25,636 - uq_methods.implementations.luq - INFO - Split text (6189 chars) into 44 sentences
2025-08-15 11:49:25,636 - uq_methods.implementations.luq - INFO - Response 3 split into 44 sentences
2025-08-15 11:49:25,638 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:25,638 - uq_methods.implementations.luq - INFO - Split text (4949 chars) into 38 sentences
2025-08-15 11:49:25,638 - uq_methods.implementations.luq - INFO - Response 4 split into 38 sentences
2025-08-15 11:49:25,640 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:25,640 - uq_methods.implementations.luq - INFO - Split text (5286 chars) into 42 sentences
2025-08-15 11:49:25,640 - uq_methods.implementations.luq - INFO - Response 5 split into 42 sentences
2025-08-15 11:49:25,643 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:25,643 - uq_methods.implementations.luq - INFO - Split text (5492 chars) into 42 sentences
2025-08-15 11:49:25,643 - uq_methods.implementations.luq - INFO - Response 6 split into 42 sentences
2025-08-15 11:49:25,645 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 34 sentences
2025-08-15 11:49:25,645 - uq_methods.implementations.luq - INFO - Split text (5483 chars) into 34 sentences
2025-08-15 11:49:25,645 - uq_methods.implementations.luq - INFO - Response 7 split into 34 sentences
2025-08-15 11:49:25,648 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:25,648 - uq_methods.implementations.luq - INFO - Split text (6434 chars) into 46 sentences
2025-08-15 11:49:25,648 - uq_methods.implementations.luq - INFO - Response 8 split into 46 sentences
2025-08-15 11:49:25,650 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:49:25,650 - uq_methods.implementations.luq - INFO - Split text (6074 chars) into 53 sentences
2025-08-15 11:49:25,650 - uq_methods.implementations.luq - INFO - Response 9 split into 53 sentences
2025-08-15 11:49:25,653 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:25,653 - uq_methods.implementations.luq - INFO - Split text (5879 chars) into 43 sentences
2025-08-15 11:49:25,653 - uq_methods.implementations.luq - INFO - Response 10 split into 43 sentences
2025-08-15 11:49:25,655 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:25,655 - uq_methods.implementations.luq - INFO - Split text (5483 chars) into 45 sentences
2025-08-15 11:49:25,655 - uq_methods.implementations.luq - INFO - Response 11 split into 45 sentences
2025-08-15 11:49:25,658 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:25,658 - uq_methods.implementations.luq - INFO - Split text (6322 chars) into 45 sentences
2025-08-15 11:49:25,658 - uq_methods.implementations.luq - INFO - Response 12 split into 45 sentences
2025-08-15 11:49:25,660 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:25,660 - uq_methods.implementations.luq - INFO - Split text (5584 chars) into 40 sentences
2025-08-15 11:49:25,660 - uq_methods.implementations.luq - INFO - Response 13 split into 40 sentences
2025-08-15 11:49:25,664 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 57 sentences
2025-08-15 11:49:25,664 - uq_methods.implementations.luq - INFO - Split text (7829 chars) into 57 sentences
2025-08-15 11:49:25,664 - uq_methods.implementations.luq - INFO - Response 14 split into 57 sentences
2025-08-15 11:49:25,666 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:25,666 - uq_methods.implementations.luq - INFO - Split text (5537 chars) into 47 sentences
2025-08-15 11:49:25,666 - uq_methods.implementations.luq - INFO - Response 15 split into 47 sentences
2025-08-15 11:49:25,668 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:25,668 - uq_methods.implementations.luq - INFO - Split text (5785 chars) into 47 sentences
2025-08-15 11:49:25,668 - uq_methods.implementations.luq - INFO - Response 16 split into 47 sentences
2025-08-15 11:49:25,671 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:25,671 - uq_methods.implementations.luq - INFO - Split text (5091 chars) into 39 sentences
2025-08-15 11:49:25,671 - uq_methods.implementations.luq - INFO - Response 17 split into 39 sentences
2025-08-15 11:49:25,673 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:25,673 - uq_methods.implementations.luq - INFO - Split text (5437 chars) into 39 sentences
2025-08-15 11:49:25,673 - uq_methods.implementations.luq - INFO - Response 18 split into 39 sentences
2025-08-15 11:49:25,675 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:25,675 - uq_methods.implementations.luq - INFO - Split text (5741 chars) into 46 sentences
2025-08-15 11:49:25,675 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:49:25,678 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:25,678 - uq_methods.implementations.luq - INFO - Split text (5374 chars) into 39 sentences
2025-08-15 11:49:25,678 - uq_methods.implementations.luq - INFO - Response 20 split into 39 sentences
2025-08-15 11:49:25,680 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:25,681 - uq_methods.implementations.luq - INFO - Split text (6463 chars) into 47 sentences
2025-08-15 11:49:25,681 - uq_methods.implementations.luq - INFO - Response 21 split into 47 sentences
2025-08-15 11:49:25,683 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:25,683 - uq_methods.implementations.luq - INFO - Split text (5563 chars) into 44 sentences
2025-08-15 11:49:25,683 - uq_methods.implementations.luq - INFO - Response 22 split into 44 sentences
2025-08-15 11:49:25,685 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:25,686 - uq_methods.implementations.luq - INFO - Split text (5999 chars) into 39 sentences
2025-08-15 11:49:25,686 - uq_methods.implementations.luq - INFO - Response 23 split into 39 sentences
2025-08-15 11:49:25,688 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:25,688 - uq_methods.implementations.luq - INFO - Split text (5656 chars) into 46 sentences
2025-08-15 11:49:25,688 - uq_methods.implementations.luq - INFO - Response 24 split into 46 sentences
2025-08-15 11:49:25,690 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:25,690 - uq_methods.implementations.luq - INFO - Split text (5906 chars) into 46 sentences
2025-08-15 11:49:25,690 - uq_methods.implementations.luq - INFO - Response 25 split into 46 sentences
2025-08-15 11:49:25,692 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:25,692 - uq_methods.implementations.luq - INFO - Split text (4561 chars) into 37 sentences
2025-08-15 11:49:25,692 - uq_methods.implementations.luq - INFO - Response 26 split into 37 sentences
2025-08-15 11:49:25,695 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 58 sentences
2025-08-15 11:49:25,695 - uq_methods.implementations.luq - INFO - Split text (6901 chars) into 58 sentences
2025-08-15 11:49:25,695 - uq_methods.implementations.luq - INFO - Response 27 split into 58 sentences
2025-08-15 11:49:25,698 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:25,698 - uq_methods.implementations.luq - INFO - Split text (6620 chars) into 51 sentences
2025-08-15 11:49:25,698 - uq_methods.implementations.luq - INFO - Response 28 split into 51 sentences
2025-08-15 11:49:25,701 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:25,701 - uq_methods.implementations.luq - INFO - Split text (5521 chars) into 43 sentences
2025-08-15 11:49:25,701 - uq_methods.implementations.luq - INFO - Response 29 split into 43 sentences
2025-08-15 11:49:25,703 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:25,703 - uq_methods.implementations.luq - INFO - Split text (4849 chars) into 39 sentences
2025-08-15 11:49:25,703 - uq_methods.implementations.luq - INFO - Response 30 split into 39 sentences
2025-08-15 11:49:25,705 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:25,705 - uq_methods.implementations.luq - INFO - Split text (5024 chars) into 41 sentences
2025-08-15 11:49:25,705 - uq_methods.implementations.luq - INFO - Response 31 split into 41 sentences
2025-08-15 11:49:25,707 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:25,707 - uq_methods.implementations.luq - INFO - Split text (5042 chars) into 40 sentences
2025-08-15 11:49:25,707 - uq_methods.implementations.luq - INFO - Response 32 split into 40 sentences
2025-08-15 11:49:25,710 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:25,710 - uq_methods.implementations.luq - INFO - Split text (6600 chars) into 49 sentences
2025-08-15 11:49:25,710 - uq_methods.implementations.luq - INFO - Response 33 split into 49 sentences
2025-08-15 11:49:25,713 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:25,713 - uq_methods.implementations.luq - INFO - Split text (7239 chars) into 52 sentences
2025-08-15 11:49:25,713 - uq_methods.implementations.luq - INFO - Response 34 split into 52 sentences
2025-08-15 11:49:25,716 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:25,716 - uq_methods.implementations.luq - INFO - Split text (7272 chars) into 50 sentences
2025-08-15 11:49:25,716 - uq_methods.implementations.luq - INFO - Response 35 split into 50 sentences
2025-08-15 11:49:25,719 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:25,719 - uq_methods.implementations.luq - INFO - Split text (6718 chars) into 47 sentences
2025-08-15 11:49:25,719 - uq_methods.implementations.luq - INFO - Response 36 split into 47 sentences
2025-08-15 11:49:25,722 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:25,722 - uq_methods.implementations.luq - INFO - Split text (7178 chars) into 48 sentences
2025-08-15 11:49:25,722 - uq_methods.implementations.luq - INFO - Response 37 split into 48 sentences
2025-08-15 11:49:25,725 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:49:25,725 - uq_methods.implementations.luq - INFO - Split text (7898 chars) into 54 sentences
2025-08-15 11:49:25,725 - uq_methods.implementations.luq - INFO - Response 38 split into 54 sentences
2025-08-15 11:49:25,728 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:25,728 - uq_methods.implementations.luq - INFO - Split text (7149 chars) into 51 sentences
2025-08-15 11:49:25,728 - uq_methods.implementations.luq - INFO - Response 39 split into 51 sentences
2025-08-15 11:49:25,731 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:25,731 - uq_methods.implementations.luq - INFO - Split text (6220 chars) into 45 sentences
2025-08-15 11:49:25,731 - uq_methods.implementations.luq - INFO - Response 40 split into 45 sentences
2025-08-15 11:49:25,733 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:25,733 - uq_methods.implementations.luq - INFO - Split text (6110 chars) into 43 sentences
2025-08-15 11:49:25,733 - uq_methods.implementations.luq - INFO - Response 41 split into 43 sentences
2025-08-15 11:49:25,736 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:25,736 - uq_methods.implementations.luq - INFO - Split text (6320 chars) into 48 sentences
2025-08-15 11:49:25,736 - uq_methods.implementations.luq - INFO - Response 42 split into 48 sentences
2025-08-15 11:49:25,739 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:25,739 - uq_methods.implementations.luq - INFO - Split text (7222 chars) into 52 sentences
2025-08-15 11:49:25,739 - uq_methods.implementations.luq - INFO - Response 43 split into 52 sentences
2025-08-15 11:49:25,742 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:25,742 - uq_methods.implementations.luq - INFO - Split text (6773 chars) into 49 sentences
2025-08-15 11:49:25,742 - uq_methods.implementations.luq - INFO - Response 44 split into 49 sentences
2025-08-15 11:49:25,742 - uq_methods.implementations.luq - INFO - Processing sample 1/44
2025-08-15 11:49:25,893 - uq_methods.implementations.luq - INFO - Processing sample 2/44
2025-08-15 11:49:26,054 - uq_methods.implementations.luq - INFO - Processing sample 3/44
2025-08-15 11:49:26,207 - uq_methods.implementations.luq - INFO - Processing sample 4/44
2025-08-15 11:49:26,339 - uq_methods.implementations.luq - INFO - Processing sample 5/44
2025-08-15 11:49:26,487 - uq_methods.implementations.luq - INFO - Processing sample 6/44
2025-08-15 11:49:26,637 - uq_methods.implementations.luq - INFO - Processing sample 7/44
2025-08-15 11:49:26,887 - uq_methods.implementations.luq - INFO - Processing sample 8/44
2025-08-15 11:49:27,049 - uq_methods.implementations.luq - INFO - Processing sample 9/44
2025-08-15 11:49:27,236 - uq_methods.implementations.luq - INFO - Processing sample 10/44
2025-08-15 11:49:27,385 - uq_methods.implementations.luq - INFO - Processing sample 11/44
2025-08-15 11:49:27,540 - uq_methods.implementations.luq - INFO - Processing sample 12/44
2025-08-15 11:49:27,694 - uq_methods.implementations.luq - INFO - Processing sample 13/44
2025-08-15 11:49:27,832 - uq_methods.implementations.luq - INFO - Processing sample 14/44
2025-08-15 11:49:28,027 - uq_methods.implementations.luq - INFO - Processing sample 15/44
2025-08-15 11:49:28,191 - uq_methods.implementations.luq - INFO - Processing sample 16/44
2025-08-15 11:49:28,354 - uq_methods.implementations.luq - INFO - Processing sample 17/44
2025-08-15 11:49:28,488 - uq_methods.implementations.luq - INFO - Processing sample 18/44
2025-08-15 11:49:28,623 - uq_methods.implementations.luq - INFO - Processing sample 19/44
2025-08-15 11:49:28,781 - uq_methods.implementations.luq - INFO - Processing sample 20/44
2025-08-15 11:49:28,916 - uq_methods.implementations.luq - INFO - Processing sample 21/44
2025-08-15 11:49:29,081 - uq_methods.implementations.luq - INFO - Processing sample 22/44
2025-08-15 11:49:29,236 - uq_methods.implementations.luq - INFO - Processing sample 23/44
2025-08-15 11:49:29,371 - uq_methods.implementations.luq - INFO - Processing sample 24/44
2025-08-15 11:49:29,531 - uq_methods.implementations.luq - INFO - Processing sample 25/44
2025-08-15 11:49:29,692 - uq_methods.implementations.luq - INFO - Processing sample 26/44
2025-08-15 11:49:29,822 - uq_methods.implementations.luq - INFO - Processing sample 27/44
2025-08-15 11:49:30,024 - uq_methods.implementations.luq - INFO - Processing sample 28/44
2025-08-15 11:49:30,201 - uq_methods.implementations.luq - INFO - Processing sample 29/44
2025-08-15 11:49:30,352 - uq_methods.implementations.luq - INFO - Processing sample 30/44
2025-08-15 11:49:30,487 - uq_methods.implementations.luq - INFO - Processing sample 31/44
2025-08-15 11:49:30,630 - uq_methods.implementations.luq - INFO - Processing sample 32/44
2025-08-15 11:49:30,768 - uq_methods.implementations.luq - INFO - Processing sample 33/44
2025-08-15 11:49:30,936 - uq_methods.implementations.luq - INFO - Processing sample 34/44
2025-08-15 11:49:31,116 - uq_methods.implementations.luq - INFO - Processing sample 35/44
2025-08-15 11:49:31,289 - uq_methods.implementations.luq - INFO - Processing sample 36/44
2025-08-15 11:49:31,455 - uq_methods.implementations.luq - INFO - Processing sample 37/44
2025-08-15 11:49:31,624 - uq_methods.implementations.luq - INFO - Processing sample 38/44
2025-08-15 11:49:31,815 - uq_methods.implementations.luq - INFO - Processing sample 39/44
2025-08-15 11:49:31,995 - uq_methods.implementations.luq - INFO - Processing sample 40/44
2025-08-15 11:49:32,151 - uq_methods.implementations.luq - INFO - Processing sample 41/44
2025-08-15 11:49:32,300 - uq_methods.implementations.luq - INFO - Processing sample 42/44
2025-08-15 11:49:32,465 - uq_methods.implementations.luq - INFO - Processing sample 43/44
2025-08-15 11:49:32,648 - uq_methods.implementations.luq - INFO - Processing sample 44/44
2025-08-15 11:49:32,957 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:32,957 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=0, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:32,957 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 28 responses
2025-08-15 11:49:32,960 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:49:32,960 - uq_methods.implementations.luq - INFO - Split text (7831 chars) into 54 sentences
2025-08-15 11:49:32,961 - uq_methods.implementations.luq - INFO - Response 1 split into 54 sentences
2025-08-15 11:49:32,964 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:49:32,964 - uq_methods.implementations.luq - INFO - Split text (7007 chars) into 53 sentences
2025-08-15 11:49:32,964 - uq_methods.implementations.luq - INFO - Response 2 split into 53 sentences
2025-08-15 11:49:32,966 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:32,966 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 38 sentences
2025-08-15 11:49:32,966 - uq_methods.implementations.luq - INFO - Response 3 split into 38 sentences
2025-08-15 11:49:32,969 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:32,969 - uq_methods.implementations.luq - INFO - Split text (6582 chars) into 44 sentences
2025-08-15 11:49:32,969 - uq_methods.implementations.luq - INFO - Response 4 split into 44 sentences
2025-08-15 11:49:32,972 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:32,972 - uq_methods.implementations.luq - INFO - Split text (6962 chars) into 48 sentences
2025-08-15 11:49:32,972 - uq_methods.implementations.luq - INFO - Response 5 split into 48 sentences
2025-08-15 11:49:32,975 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:32,975 - uq_methods.implementations.luq - INFO - Split text (6379 chars) into 46 sentences
2025-08-15 11:49:32,975 - uq_methods.implementations.luq - INFO - Response 6 split into 46 sentences
2025-08-15 11:49:32,978 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:32,978 - uq_methods.implementations.luq - INFO - Split text (6546 chars) into 50 sentences
2025-08-15 11:49:32,978 - uq_methods.implementations.luq - INFO - Response 7 split into 50 sentences
2025-08-15 11:49:32,981 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:32,981 - uq_methods.implementations.luq - INFO - Split text (6556 chars) into 47 sentences
2025-08-15 11:49:32,981 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:49:32,983 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:32,983 - uq_methods.implementations.luq - INFO - Split text (6167 chars) into 49 sentences
2025-08-15 11:49:32,984 - uq_methods.implementations.luq - INFO - Response 9 split into 49 sentences
2025-08-15 11:49:32,986 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:32,986 - uq_methods.implementations.luq - INFO - Split text (6951 chars) into 48 sentences
2025-08-15 11:49:32,987 - uq_methods.implementations.luq - INFO - Response 10 split into 48 sentences
2025-08-15 11:49:32,989 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:32,989 - uq_methods.implementations.luq - INFO - Split text (6263 chars) into 43 sentences
2025-08-15 11:49:32,989 - uq_methods.implementations.luq - INFO - Response 11 split into 43 sentences
2025-08-15 11:49:32,992 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:32,992 - uq_methods.implementations.luq - INFO - Split text (6123 chars) into 42 sentences
2025-08-15 11:49:32,992 - uq_methods.implementations.luq - INFO - Response 12 split into 42 sentences
2025-08-15 11:49:32,995 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:49:32,995 - uq_methods.implementations.luq - INFO - Split text (7118 chars) into 54 sentences
2025-08-15 11:49:32,995 - uq_methods.implementations.luq - INFO - Response 13 split into 54 sentences
2025-08-15 11:49:32,998 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:32,998 - uq_methods.implementations.luq - INFO - Split text (6405 chars) into 50 sentences
2025-08-15 11:49:32,998 - uq_methods.implementations.luq - INFO - Response 14 split into 50 sentences
2025-08-15 11:49:33,001 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:33,001 - uq_methods.implementations.luq - INFO - Split text (6574 chars) into 48 sentences
2025-08-15 11:49:33,001 - uq_methods.implementations.luq - INFO - Response 15 split into 48 sentences
2025-08-15 11:49:33,004 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:33,004 - uq_methods.implementations.luq - INFO - Split text (7415 chars) into 52 sentences
2025-08-15 11:49:33,004 - uq_methods.implementations.luq - INFO - Response 16 split into 52 sentences
2025-08-15 11:49:33,007 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:33,007 - uq_methods.implementations.luq - INFO - Split text (5627 chars) into 37 sentences
2025-08-15 11:49:33,007 - uq_methods.implementations.luq - INFO - Response 17 split into 37 sentences
2025-08-15 11:49:33,009 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:33,009 - uq_methods.implementations.luq - INFO - Split text (5337 chars) into 44 sentences
2025-08-15 11:49:33,009 - uq_methods.implementations.luq - INFO - Response 18 split into 44 sentences
2025-08-15 11:49:33,012 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:33,012 - uq_methods.implementations.luq - INFO - Split text (6004 chars) into 46 sentences
2025-08-15 11:49:33,012 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:49:33,015 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:33,015 - uq_methods.implementations.luq - INFO - Split text (5828 chars) into 43 sentences
2025-08-15 11:49:33,015 - uq_methods.implementations.luq - INFO - Response 20 split into 43 sentences
2025-08-15 11:49:33,018 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:33,018 - uq_methods.implementations.luq - INFO - Split text (6615 chars) into 48 sentences
2025-08-15 11:49:33,018 - uq_methods.implementations.luq - INFO - Response 21 split into 48 sentences
2025-08-15 11:49:33,021 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:33,021 - uq_methods.implementations.luq - INFO - Split text (5704 chars) into 43 sentences
2025-08-15 11:49:33,021 - uq_methods.implementations.luq - INFO - Response 22 split into 43 sentences
2025-08-15 11:49:33,023 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:33,023 - uq_methods.implementations.luq - INFO - Split text (5895 chars) into 41 sentences
2025-08-15 11:49:33,023 - uq_methods.implementations.luq - INFO - Response 23 split into 41 sentences
2025-08-15 11:49:33,026 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:33,026 - uq_methods.implementations.luq - INFO - Split text (5252 chars) into 36 sentences
2025-08-15 11:49:33,026 - uq_methods.implementations.luq - INFO - Response 24 split into 36 sentences
2025-08-15 11:49:33,029 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:33,029 - uq_methods.implementations.luq - INFO - Split text (6717 chars) into 51 sentences
2025-08-15 11:49:33,029 - uq_methods.implementations.luq - INFO - Response 25 split into 51 sentences
2025-08-15 11:49:33,031 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:33,031 - uq_methods.implementations.luq - INFO - Split text (6356 chars) into 40 sentences
2025-08-15 11:49:33,031 - uq_methods.implementations.luq - INFO - Response 26 split into 40 sentences
2025-08-15 11:49:33,034 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:33,034 - uq_methods.implementations.luq - INFO - Split text (5607 chars) into 44 sentences
2025-08-15 11:49:33,034 - uq_methods.implementations.luq - INFO - Response 27 split into 44 sentences
2025-08-15 11:49:33,036 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:33,036 - uq_methods.implementations.luq - INFO - Split text (6468 chars) into 48 sentences
2025-08-15 11:49:33,036 - uq_methods.implementations.luq - INFO - Response 28 split into 48 sentences
2025-08-15 11:49:33,036 - uq_methods.implementations.luq - INFO - Processing sample 1/28
2025-08-15 11:49:33,169 - uq_methods.implementations.luq - INFO - Processing sample 2/28
2025-08-15 11:49:33,296 - uq_methods.implementations.luq - INFO - Processing sample 3/28
2025-08-15 11:49:33,386 - uq_methods.implementations.luq - INFO - Processing sample 4/28
2025-08-15 11:49:33,490 - uq_methods.implementations.luq - INFO - Processing sample 5/28
2025-08-15 11:49:33,601 - uq_methods.implementations.luq - INFO - Processing sample 6/28
2025-08-15 11:49:33,707 - uq_methods.implementations.luq - INFO - Processing sample 7/28
2025-08-15 11:49:33,822 - uq_methods.implementations.luq - INFO - Processing sample 8/28
2025-08-15 11:49:33,930 - uq_methods.implementations.luq - INFO - Processing sample 9/28
2025-08-15 11:49:34,043 - uq_methods.implementations.luq - INFO - Processing sample 10/28
2025-08-15 11:49:34,153 - uq_methods.implementations.luq - INFO - Processing sample 11/28
2025-08-15 11:49:34,255 - uq_methods.implementations.luq - INFO - Processing sample 12/28
2025-08-15 11:49:34,352 - uq_methods.implementations.luq - INFO - Processing sample 13/28
2025-08-15 11:49:34,477 - uq_methods.implementations.luq - INFO - Processing sample 14/28
2025-08-15 11:49:34,588 - uq_methods.implementations.luq - INFO - Processing sample 15/28
2025-08-15 11:49:34,693 - uq_methods.implementations.luq - INFO - Processing sample 16/28
2025-08-15 11:49:34,806 - uq_methods.implementations.luq - INFO - Processing sample 17/28
2025-08-15 11:49:34,887 - uq_methods.implementations.luq - INFO - Processing sample 18/28
2025-08-15 11:49:34,983 - uq_methods.implementations.luq - INFO - Processing sample 19/28
2025-08-15 11:49:35,083 - uq_methods.implementations.luq - INFO - Processing sample 20/28
2025-08-15 11:49:35,177 - uq_methods.implementations.luq - INFO - Processing sample 21/28
2025-08-15 11:49:35,281 - uq_methods.implementations.luq - INFO - Processing sample 22/28
2025-08-15 11:49:35,375 - uq_methods.implementations.luq - INFO - Processing sample 23/28
2025-08-15 11:49:35,618 - uq_methods.implementations.luq - INFO - Processing sample 24/28
2025-08-15 11:49:35,696 - uq_methods.implementations.luq - INFO - Processing sample 25/28
2025-08-15 11:49:35,806 - uq_methods.implementations.luq - INFO - Processing sample 26/28
2025-08-15 11:49:35,893 - uq_methods.implementations.luq - INFO - Processing sample 27/28
2025-08-15 11:49:35,988 - uq_methods.implementations.luq - INFO - Processing sample 28/28
2025-08-15 11:49:36,146 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:36,146 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=1, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:36,147 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 19 responses
2025-08-15 11:49:36,149 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:49:36,149 - uq_methods.implementations.luq - INFO - Split text (4609 chars) into 33 sentences
2025-08-15 11:49:36,149 - uq_methods.implementations.luq - INFO - Response 1 split into 33 sentences
2025-08-15 11:49:36,152 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:36,152 - uq_methods.implementations.luq - INFO - Split text (6010 chars) into 39 sentences
2025-08-15 11:49:36,152 - uq_methods.implementations.luq - INFO - Response 2 split into 39 sentences
2025-08-15 11:49:36,154 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:36,154 - uq_methods.implementations.luq - INFO - Split text (5201 chars) into 40 sentences
2025-08-15 11:49:36,154 - uq_methods.implementations.luq - INFO - Response 3 split into 40 sentences
2025-08-15 11:49:36,157 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:36,157 - uq_methods.implementations.luq - INFO - Split text (5077 chars) into 35 sentences
2025-08-15 11:49:36,157 - uq_methods.implementations.luq - INFO - Response 4 split into 35 sentences
2025-08-15 11:49:36,160 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:36,160 - uq_methods.implementations.luq - INFO - Split text (5551 chars) into 46 sentences
2025-08-15 11:49:36,160 - uq_methods.implementations.luq - INFO - Response 5 split into 46 sentences
2025-08-15 11:49:36,162 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:36,162 - uq_methods.implementations.luq - INFO - Split text (5159 chars) into 43 sentences
2025-08-15 11:49:36,162 - uq_methods.implementations.luq - INFO - Response 6 split into 43 sentences
2025-08-15 11:49:36,164 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:36,164 - uq_methods.implementations.luq - INFO - Split text (5132 chars) into 42 sentences
2025-08-15 11:49:36,164 - uq_methods.implementations.luq - INFO - Response 7 split into 42 sentences
2025-08-15 11:49:36,167 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:36,167 - uq_methods.implementations.luq - INFO - Split text (5820 chars) into 42 sentences
2025-08-15 11:49:36,167 - uq_methods.implementations.luq - INFO - Response 8 split into 42 sentences
2025-08-15 11:49:36,170 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:49:36,170 - uq_methods.implementations.luq - INFO - Split text (6456 chars) into 43 sentences
2025-08-15 11:49:36,170 - uq_methods.implementations.luq - INFO - Response 9 split into 43 sentences
2025-08-15 11:49:36,173 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:36,173 - uq_methods.implementations.luq - INFO - Split text (6082 chars) into 42 sentences
2025-08-15 11:49:36,173 - uq_methods.implementations.luq - INFO - Response 10 split into 42 sentences
2025-08-15 11:49:36,175 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:36,175 - uq_methods.implementations.luq - INFO - Split text (5620 chars) into 35 sentences
2025-08-15 11:49:36,175 - uq_methods.implementations.luq - INFO - Response 11 split into 35 sentences
2025-08-15 11:49:36,178 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:36,178 - uq_methods.implementations.luq - INFO - Split text (5799 chars) into 40 sentences
2025-08-15 11:49:36,178 - uq_methods.implementations.luq - INFO - Response 12 split into 40 sentences
2025-08-15 11:49:36,181 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:36,181 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 44 sentences
2025-08-15 11:49:36,181 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:49:36,184 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:36,184 - uq_methods.implementations.luq - INFO - Split text (6803 chars) into 47 sentences
2025-08-15 11:49:36,184 - uq_methods.implementations.luq - INFO - Response 14 split into 47 sentences
2025-08-15 11:49:36,186 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:49:36,186 - uq_methods.implementations.luq - INFO - Split text (4477 chars) into 31 sentences
2025-08-15 11:49:36,186 - uq_methods.implementations.luq - INFO - Response 15 split into 31 sentences
2025-08-15 11:49:36,189 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:49:36,189 - uq_methods.implementations.luq - INFO - Split text (4782 chars) into 35 sentences
2025-08-15 11:49:36,189 - uq_methods.implementations.luq - INFO - Response 16 split into 35 sentences
2025-08-15 11:49:36,191 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:36,191 - uq_methods.implementations.luq - INFO - Split text (5581 chars) into 37 sentences
2025-08-15 11:49:36,191 - uq_methods.implementations.luq - INFO - Response 17 split into 37 sentences
2025-08-15 11:49:36,194 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:36,194 - uq_methods.implementations.luq - INFO - Split text (5524 chars) into 40 sentences
2025-08-15 11:49:36,194 - uq_methods.implementations.luq - INFO - Response 18 split into 40 sentences
2025-08-15 11:49:36,196 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:49:36,196 - uq_methods.implementations.luq - INFO - Split text (5049 chars) into 30 sentences
2025-08-15 11:49:36,196 - uq_methods.implementations.luq - INFO - Response 19 split into 30 sentences
2025-08-15 11:49:36,196 - uq_methods.implementations.luq - INFO - Processing sample 1/19
2025-08-15 11:49:36,248 - uq_methods.implementations.luq - INFO - Processing sample 2/19
2025-08-15 11:49:36,307 - uq_methods.implementations.luq - INFO - Processing sample 3/19
2025-08-15 11:49:36,367 - uq_methods.implementations.luq - INFO - Processing sample 4/19
2025-08-15 11:49:36,422 - uq_methods.implementations.luq - INFO - Processing sample 5/19
2025-08-15 11:49:36,492 - uq_methods.implementations.luq - INFO - Processing sample 6/19
2025-08-15 11:49:36,558 - uq_methods.implementations.luq - INFO - Processing sample 7/19
2025-08-15 11:49:36,623 - uq_methods.implementations.luq - INFO - Processing sample 8/19
2025-08-15 11:49:36,687 - uq_methods.implementations.luq - INFO - Processing sample 9/19
2025-08-15 11:49:36,753 - uq_methods.implementations.luq - INFO - Processing sample 10/19
2025-08-15 11:49:36,815 - uq_methods.implementations.luq - INFO - Processing sample 11/19
2025-08-15 11:49:36,867 - uq_methods.implementations.luq - INFO - Processing sample 12/19
2025-08-15 11:49:36,927 - uq_methods.implementations.luq - INFO - Processing sample 13/19
2025-08-15 11:49:36,993 - uq_methods.implementations.luq - INFO - Processing sample 14/19
2025-08-15 11:49:37,063 - uq_methods.implementations.luq - INFO - Processing sample 15/19
2025-08-15 11:49:37,109 - uq_methods.implementations.luq - INFO - Processing sample 16/19
2025-08-15 11:49:37,162 - uq_methods.implementations.luq - INFO - Processing sample 17/19
2025-08-15 11:49:37,219 - uq_methods.implementations.luq - INFO - Processing sample 18/19
2025-08-15 11:49:37,278 - uq_methods.implementations.luq - INFO - Processing sample 19/19
2025-08-15 11:49:37,344 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:37,344 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=9, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:37,344 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:37,347 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:37,347 - uq_methods.implementations.luq - INFO - Split text (5852 chars) into 47 sentences
2025-08-15 11:49:37,347 - uq_methods.implementations.luq - INFO - Response 1 split into 47 sentences
2025-08-15 11:49:37,349 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:49:37,349 - uq_methods.implementations.luq - INFO - Split text (6337 chars) into 53 sentences
2025-08-15 11:49:37,349 - uq_methods.implementations.luq - INFO - Response 2 split into 53 sentences
2025-08-15 11:49:37,352 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:37,352 - uq_methods.implementations.luq - INFO - Split text (5892 chars) into 47 sentences
2025-08-15 11:49:37,352 - uq_methods.implementations.luq - INFO - Response 3 split into 47 sentences
2025-08-15 11:49:37,354 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:49:37,354 - uq_methods.implementations.luq - INFO - Split text (5909 chars) into 51 sentences
2025-08-15 11:49:37,354 - uq_methods.implementations.luq - INFO - Response 4 split into 51 sentences
2025-08-15 11:49:37,356 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:49:37,356 - uq_methods.implementations.luq - INFO - Split text (5224 chars) into 36 sentences
2025-08-15 11:49:37,356 - uq_methods.implementations.luq - INFO - Response 5 split into 36 sentences
2025-08-15 11:49:37,359 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 55 sentences
2025-08-15 11:49:37,359 - uq_methods.implementations.luq - INFO - Split text (6440 chars) into 55 sentences
2025-08-15 11:49:37,359 - uq_methods.implementations.luq - INFO - Response 6 split into 55 sentences
2025-08-15 11:49:37,362 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:37,362 - uq_methods.implementations.luq - INFO - Split text (6482 chars) into 49 sentences
2025-08-15 11:49:37,362 - uq_methods.implementations.luq - INFO - Response 7 split into 49 sentences
2025-08-15 11:49:37,364 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:37,364 - uq_methods.implementations.luq - INFO - Split text (6201 chars) into 45 sentences
2025-08-15 11:49:37,364 - uq_methods.implementations.luq - INFO - Response 8 split into 45 sentences
2025-08-15 11:49:37,368 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 55 sentences
2025-08-15 11:49:37,368 - uq_methods.implementations.luq - INFO - Split text (8300 chars) into 55 sentences
2025-08-15 11:49:37,368 - uq_methods.implementations.luq - INFO - Response 9 split into 55 sentences
2025-08-15 11:49:37,371 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:49:37,371 - uq_methods.implementations.luq - INFO - Split text (7433 chars) into 56 sentences
2025-08-15 11:49:37,371 - uq_methods.implementations.luq - INFO - Response 10 split into 56 sentences
2025-08-15 11:49:37,373 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:37,373 - uq_methods.implementations.luq - INFO - Split text (6484 chars) into 48 sentences
2025-08-15 11:49:37,373 - uq_methods.implementations.luq - INFO - Response 11 split into 48 sentences
2025-08-15 11:49:37,376 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:37,376 - uq_methods.implementations.luq - INFO - Split text (6283 chars) into 46 sentences
2025-08-15 11:49:37,376 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:49:37,378 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:37,378 - uq_methods.implementations.luq - INFO - Split text (5290 chars) into 44 sentences
2025-08-15 11:49:37,378 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:49:37,381 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:37,381 - uq_methods.implementations.luq - INFO - Split text (6396 chars) into 48 sentences
2025-08-15 11:49:37,381 - uq_methods.implementations.luq - INFO - Response 14 split into 48 sentences
2025-08-15 11:49:37,383 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:37,383 - uq_methods.implementations.luq - INFO - Split text (5609 chars) into 45 sentences
2025-08-15 11:49:37,383 - uq_methods.implementations.luq - INFO - Response 15 split into 45 sentences
2025-08-15 11:49:37,385 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:37,385 - uq_methods.implementations.luq - INFO - Split text (4974 chars) into 39 sentences
2025-08-15 11:49:37,385 - uq_methods.implementations.luq - INFO - Response 16 split into 39 sentences
2025-08-15 11:49:37,388 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:37,388 - uq_methods.implementations.luq - INFO - Split text (4901 chars) into 44 sentences
2025-08-15 11:49:37,388 - uq_methods.implementations.luq - INFO - Response 17 split into 44 sentences
2025-08-15 11:49:37,390 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:37,390 - uq_methods.implementations.luq - INFO - Split text (4762 chars) into 40 sentences
2025-08-15 11:49:37,390 - uq_methods.implementations.luq - INFO - Response 18 split into 40 sentences
2025-08-15 11:49:37,392 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:37,392 - uq_methods.implementations.luq - INFO - Split text (4857 chars) into 38 sentences
2025-08-15 11:49:37,392 - uq_methods.implementations.luq - INFO - Response 19 split into 38 sentences
2025-08-15 11:49:37,394 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:37,394 - uq_methods.implementations.luq - INFO - Split text (5548 chars) into 41 sentences
2025-08-15 11:49:37,394 - uq_methods.implementations.luq - INFO - Response 20 split into 41 sentences
2025-08-15 11:49:37,397 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:37,397 - uq_methods.implementations.luq - INFO - Split text (6039 chars) into 42 sentences
2025-08-15 11:49:37,397 - uq_methods.implementations.luq - INFO - Response 21 split into 42 sentences
2025-08-15 11:49:37,399 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:49:37,399 - uq_methods.implementations.luq - INFO - Split text (4756 chars) into 30 sentences
2025-08-15 11:49:37,399 - uq_methods.implementations.luq - INFO - Response 22 split into 30 sentences
2025-08-15 11:49:37,401 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:37,401 - uq_methods.implementations.luq - INFO - Split text (5266 chars) into 42 sentences
2025-08-15 11:49:37,401 - uq_methods.implementations.luq - INFO - Response 23 split into 42 sentences
2025-08-15 11:49:37,403 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:37,403 - uq_methods.implementations.luq - INFO - Split text (6358 chars) into 47 sentences
2025-08-15 11:49:37,404 - uq_methods.implementations.luq - INFO - Response 24 split into 47 sentences
2025-08-15 11:49:37,406 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:49:37,406 - uq_methods.implementations.luq - INFO - Split text (6057 chars) into 46 sentences
2025-08-15 11:49:37,406 - uq_methods.implementations.luq - INFO - Response 25 split into 46 sentences
2025-08-15 11:49:37,409 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:37,409 - uq_methods.implementations.luq - INFO - Split text (6673 chars) into 48 sentences
2025-08-15 11:49:37,409 - uq_methods.implementations.luq - INFO - Response 26 split into 48 sentences
2025-08-15 11:49:37,411 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:37,411 - uq_methods.implementations.luq - INFO - Split text (5112 chars) into 38 sentences
2025-08-15 11:49:37,411 - uq_methods.implementations.luq - INFO - Response 27 split into 38 sentences
2025-08-15 11:49:37,414 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:37,414 - uq_methods.implementations.luq - INFO - Split text (6605 chars) into 45 sentences
2025-08-15 11:49:37,414 - uq_methods.implementations.luq - INFO - Response 28 split into 45 sentences
2025-08-15 11:49:37,416 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:37,416 - uq_methods.implementations.luq - INFO - Split text (6053 chars) into 49 sentences
2025-08-15 11:49:37,416 - uq_methods.implementations.luq - INFO - Response 29 split into 49 sentences
2025-08-15 11:49:37,418 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:37,418 - uq_methods.implementations.luq - INFO - Split text (5497 chars) into 41 sentences
2025-08-15 11:49:37,418 - uq_methods.implementations.luq - INFO - Response 30 split into 41 sentences
2025-08-15 11:49:37,418 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:37,528 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:37,651 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:49:37,759 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:49:37,876 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:49:37,959 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:49:38,085 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:49:38,198 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:49:38,302 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:49:38,428 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:49:38,557 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:49:38,669 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:49:38,775 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:49:38,876 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:49:38,987 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:49:39,090 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:49:39,180 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:49:39,281 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:49:39,373 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:49:39,460 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:49:39,556 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:49:39,652 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:49:39,722 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:49:39,819 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:49:39,927 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:49:40,033 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:49:40,144 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:49:40,232 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:49:40,336 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:49:40,448 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:49:40,587 - uq_analysis.data_processor - ERROR - Failed to save result: 'update' command document too large
2025-08-15 11:49:40,588 - uq_analysis.progress_manager - ERROR - [counterfactual_qa] LUQUQ failed for group (seed=6, input_hash=42388941): 'update' command document too large
2025-08-15 11:49:40,588 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:49:40,590 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:49:40,590 - uq_methods.implementations.luq - INFO - Split text (5232 chars) into 39 sentences
2025-08-15 11:49:40,590 - uq_methods.implementations.luq - INFO - Response 1 split into 39 sentences
2025-08-15 11:49:40,592 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:40,592 - uq_methods.implementations.luq - INFO - Split text (4914 chars) into 38 sentences
2025-08-15 11:49:40,592 - uq_methods.implementations.luq - INFO - Response 2 split into 38 sentences
2025-08-15 11:49:40,595 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:49:40,595 - uq_methods.implementations.luq - INFO - Split text (5366 chars) into 41 sentences
2025-08-15 11:49:40,595 - uq_methods.implementations.luq - INFO - Response 3 split into 41 sentences
2025-08-15 11:49:40,597 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:49:40,597 - uq_methods.implementations.luq - INFO - Split text (5098 chars) into 42 sentences
2025-08-15 11:49:40,597 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:49:40,599 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:40,599 - uq_methods.implementations.luq - INFO - Split text (5923 chars) into 52 sentences
2025-08-15 11:49:40,599 - uq_methods.implementations.luq - INFO - Response 5 split into 52 sentences
2025-08-15 11:49:40,601 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 34 sentences
2025-08-15 11:49:40,601 - uq_methods.implementations.luq - INFO - Split text (4446 chars) into 34 sentences
2025-08-15 11:49:40,601 - uq_methods.implementations.luq - INFO - Response 6 split into 34 sentences
2025-08-15 11:49:40,604 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:40,604 - uq_methods.implementations.luq - INFO - Split text (6058 chars) into 50 sentences
2025-08-15 11:49:40,604 - uq_methods.implementations.luq - INFO - Response 7 split into 50 sentences
2025-08-15 11:49:40,606 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 59 sentences
2025-08-15 11:49:40,606 - uq_methods.implementations.luq - INFO - Split text (5784 chars) into 59 sentences
2025-08-15 11:49:40,606 - uq_methods.implementations.luq - INFO - Response 8 split into 59 sentences
2025-08-15 11:49:40,609 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:40,609 - uq_methods.implementations.luq - INFO - Split text (5934 chars) into 48 sentences
2025-08-15 11:49:40,609 - uq_methods.implementations.luq - INFO - Response 9 split into 48 sentences
2025-08-15 11:49:40,612 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:49:40,612 - uq_methods.implementations.luq - INFO - Split text (6029 chars) into 50 sentences
2025-08-15 11:49:40,612 - uq_methods.implementations.luq - INFO - Response 10 split into 50 sentences
2025-08-15 11:49:40,614 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:40,614 - uq_methods.implementations.luq - INFO - Split text (5693 chars) into 45 sentences
2025-08-15 11:49:40,614 - uq_methods.implementations.luq - INFO - Response 11 split into 45 sentences
2025-08-15 11:49:40,617 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:49:40,617 - uq_methods.implementations.luq - INFO - Split text (6548 chars) into 52 sentences
2025-08-15 11:49:40,617 - uq_methods.implementations.luq - INFO - Response 12 split into 52 sentences
2025-08-15 11:49:40,619 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:40,619 - uq_methods.implementations.luq - INFO - Split text (5497 chars) into 44 sentences
2025-08-15 11:49:40,619 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:49:40,622 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:49:40,622 - uq_methods.implementations.luq - INFO - Split text (6147 chars) into 54 sentences
2025-08-15 11:49:40,622 - uq_methods.implementations.luq - INFO - Response 14 split into 54 sentences
2025-08-15 11:49:40,625 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:49:40,625 - uq_methods.implementations.luq - INFO - Split text (6398 chars) into 47 sentences
2025-08-15 11:49:40,625 - uq_methods.implementations.luq - INFO - Response 15 split into 47 sentences
2025-08-15 11:49:40,627 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:40,627 - uq_methods.implementations.luq - INFO - Split text (6030 chars) into 44 sentences
2025-08-15 11:49:40,627 - uq_methods.implementations.luq - INFO - Response 16 split into 44 sentences
2025-08-15 11:49:40,630 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:40,630 - uq_methods.implementations.luq - INFO - Split text (5957 chars) into 38 sentences
2025-08-15 11:49:40,630 - uq_methods.implementations.luq - INFO - Response 17 split into 38 sentences
2025-08-15 11:49:40,632 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:40,632 - uq_methods.implementations.luq - INFO - Split text (6264 chars) into 49 sentences
2025-08-15 11:49:40,632 - uq_methods.implementations.luq - INFO - Response 18 split into 49 sentences
2025-08-15 11:49:40,634 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:49:40,634 - uq_methods.implementations.luq - INFO - Split text (4872 chars) into 45 sentences
2025-08-15 11:49:40,634 - uq_methods.implementations.luq - INFO - Response 19 split into 45 sentences
2025-08-15 11:49:40,637 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:49:40,637 - uq_methods.implementations.luq - INFO - Split text (5382 chars) into 44 sentences
2025-08-15 11:49:40,637 - uq_methods.implementations.luq - INFO - Response 20 split into 44 sentences
2025-08-15 11:49:40,639 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:49:40,639 - uq_methods.implementations.luq - INFO - Split text (5999 chars) into 54 sentences
2025-08-15 11:49:40,639 - uq_methods.implementations.luq - INFO - Response 21 split into 54 sentences
2025-08-15 11:49:40,642 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 63 sentences
2025-08-15 11:49:40,642 - uq_methods.implementations.luq - INFO - Split text (6385 chars) into 63 sentences
2025-08-15 11:49:40,642 - uq_methods.implementations.luq - INFO - Response 22 split into 63 sentences
2025-08-15 11:49:40,645 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:49:40,645 - uq_methods.implementations.luq - INFO - Split text (6773 chars) into 56 sentences
2025-08-15 11:49:40,645 - uq_methods.implementations.luq - INFO - Response 23 split into 56 sentences
2025-08-15 11:49:40,648 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:49:40,648 - uq_methods.implementations.luq - INFO - Split text (5656 chars) into 48 sentences
2025-08-15 11:49:40,648 - uq_methods.implementations.luq - INFO - Response 24 split into 48 sentences
2025-08-15 11:49:40,650 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:49:40,650 - uq_methods.implementations.luq - INFO - Split text (5119 chars) into 38 sentences
2025-08-15 11:49:40,650 - uq_methods.implementations.luq - INFO - Response 25 split into 38 sentences
2025-08-15 11:49:40,652 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:49:40,652 - uq_methods.implementations.luq - INFO - Split text (5394 chars) into 49 sentences
2025-08-15 11:49:40,652 - uq_methods.implementations.luq - INFO - Response 26 split into 49 sentences
2025-08-15 11:49:40,654 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:40,654 - uq_methods.implementations.luq - INFO - Split text (5014 chars) into 40 sentences
2025-08-15 11:49:40,654 - uq_methods.implementations.luq - INFO - Response 27 split into 40 sentences
2025-08-15 11:49:40,657 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:49:40,657 - uq_methods.implementations.luq - INFO - Split text (5109 chars) into 40 sentences
2025-08-15 11:49:40,657 - uq_methods.implementations.luq - INFO - Response 28 split into 40 sentences
2025-08-15 11:49:40,659 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 60 sentences
2025-08-15 11:49:40,659 - uq_methods.implementations.luq - INFO - Split text (6184 chars) into 60 sentences
2025-08-15 11:49:40,659 - uq_methods.implementations.luq - INFO - Response 29 split into 60 sentences
2025-08-15 11:49:40,661 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:49:40,661 - uq_methods.implementations.luq - INFO - Split text (4632 chars) into 37 sentences
2025-08-15 11:49:40,661 - uq_methods.implementations.luq - INFO - Response 30 split into 37 sentences
2025-08-15 11:49:40,661 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:49:40,755 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:49:40,821 - uq_analysis.data_processor - INFO - Disconnected from MongoDB
2025-08-15 11:56:35,763 - __main__ - INFO - Initializing UQ analysis system...
2025-08-15 11:56:36,968 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-08-15 11:56:36,968 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-08-15 11:56:37,456 - datasets - INFO - PyTorch version 2.6.0 available.
2025-08-15 11:56:37,667 - uq_analysis.method_loader - INFO - Discovered 11 UQ methods: ['EigValLaplacianNLIUQ', 'LofreeCPUQ', 'LUQUQ', 'EmbeddingE5UQ', 'SemanticEntropyNLIUQ', 'EigValLaplacianJaccardUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ', 'EmbeddingQwenUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ']
2025-08-15 11:56:37,667 - __main__ - INFO - Discovered 11 UQ methods
2025-08-15 11:56:37,669 - uq_analysis.data_processor - INFO - Connected to MongoDB: localhost:27017/LLM-UQ
2025-08-15 11:56:37,669 - __main__ - INFO - Initialization complete
2025-08-15 11:56:37,669 - uq_analysis.progress_manager - INFO - Starting UQ analysis with 1 tasks
2025-08-15 11:56:37,669 - __main__ - INFO - Processing task: counterfactual_qa
2025-08-15 11:56:39,954 - uq_analysis.method_loader - INFO - Successfully loaded 1 methods: ['LUQUQ']
2025-08-15 11:56:39,954 - __main__ - INFO - Processing counterfactual_qa/counterfactual_data
2025-08-15 11:56:39,965 - uq_analysis.data_processor - INFO - Found 10 groups for counterfactual_qa/counterfactual_data
2025-08-15 11:56:39,965 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_LUQ_counterfactual...
2025-08-15 11:56:39,966 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_LUQ_counterfactual
2025-08-15 11:56:39,966 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 10 pending, 10 total
2025-08-15 11:56:39,966 - uq_analysis.progress_manager - INFO - Starting task counterfactual_qa/counterfactual_data: 10 groups, 1 methods each
2025-08-15 11:56:39,966 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:56:39,970 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:39,970 - uq_methods.implementations.luq - INFO - Split text (5590 chars) into 43 sentences
2025-08-15 11:56:39,970 - uq_methods.implementations.luq - INFO - Response 1 split into 43 sentences
2025-08-15 11:56:39,972 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:39,972 - uq_methods.implementations.luq - INFO - Split text (4648 chars) into 40 sentences
2025-08-15 11:56:39,972 - uq_methods.implementations.luq - INFO - Response 2 split into 40 sentences
2025-08-15 11:56:39,975 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 32 sentences
2025-08-15 11:56:39,975 - uq_methods.implementations.luq - INFO - Split text (5184 chars) into 32 sentences
2025-08-15 11:56:39,975 - uq_methods.implementations.luq - INFO - Response 3 split into 32 sentences
2025-08-15 11:56:39,977 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:39,977 - uq_methods.implementations.luq - INFO - Split text (5739 chars) into 42 sentences
2025-08-15 11:56:39,977 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:56:39,979 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:56:39,979 - uq_methods.implementations.luq - INFO - Split text (5019 chars) into 33 sentences
2025-08-15 11:56:39,979 - uq_methods.implementations.luq - INFO - Response 5 split into 33 sentences
2025-08-15 11:56:39,982 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:39,982 - uq_methods.implementations.luq - INFO - Split text (5354 chars) into 36 sentences
2025-08-15 11:56:39,982 - uq_methods.implementations.luq - INFO - Response 6 split into 36 sentences
2025-08-15 11:56:39,984 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:39,984 - uq_methods.implementations.luq - INFO - Split text (5809 chars) into 35 sentences
2025-08-15 11:56:39,984 - uq_methods.implementations.luq - INFO - Response 7 split into 35 sentences
2025-08-15 11:56:39,987 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:39,987 - uq_methods.implementations.luq - INFO - Split text (6877 chars) into 41 sentences
2025-08-15 11:56:39,987 - uq_methods.implementations.luq - INFO - Response 8 split into 41 sentences
2025-08-15 11:56:39,990 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:39,990 - uq_methods.implementations.luq - INFO - Split text (7039 chars) into 42 sentences
2025-08-15 11:56:39,990 - uq_methods.implementations.luq - INFO - Response 9 split into 42 sentences
2025-08-15 11:56:39,993 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:39,993 - uq_methods.implementations.luq - INFO - Split text (7868 chars) into 49 sentences
2025-08-15 11:56:39,993 - uq_methods.implementations.luq - INFO - Response 10 split into 49 sentences
2025-08-15 11:56:39,995 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:56:39,995 - uq_methods.implementations.luq - INFO - Split text (4593 chars) into 25 sentences
2025-08-15 11:56:39,995 - uq_methods.implementations.luq - INFO - Response 11 split into 25 sentences
2025-08-15 11:56:39,998 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:39,998 - uq_methods.implementations.luq - INFO - Split text (5815 chars) into 37 sentences
2025-08-15 11:56:39,998 - uq_methods.implementations.luq - INFO - Response 12 split into 37 sentences
2025-08-15 11:56:40,000 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:40,000 - uq_methods.implementations.luq - INFO - Split text (6594 chars) into 40 sentences
2025-08-15 11:56:40,000 - uq_methods.implementations.luq - INFO - Response 13 split into 40 sentences
2025-08-15 11:56:40,003 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:56:40,003 - uq_methods.implementations.luq - INFO - Split text (5683 chars) into 30 sentences
2025-08-15 11:56:40,003 - uq_methods.implementations.luq - INFO - Response 14 split into 30 sentences
2025-08-15 11:56:40,006 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:40,006 - uq_methods.implementations.luq - INFO - Split text (6747 chars) into 44 sentences
2025-08-15 11:56:40,006 - uq_methods.implementations.luq - INFO - Response 15 split into 44 sentences
2025-08-15 11:56:40,009 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:40,009 - uq_methods.implementations.luq - INFO - Split text (7245 chars) into 46 sentences
2025-08-15 11:56:40,009 - uq_methods.implementations.luq - INFO - Response 16 split into 46 sentences
2025-08-15 11:56:40,012 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:40,012 - uq_methods.implementations.luq - INFO - Split text (7117 chars) into 43 sentences
2025-08-15 11:56:40,012 - uq_methods.implementations.luq - INFO - Response 17 split into 43 sentences
2025-08-15 11:56:40,014 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:56:40,014 - uq_methods.implementations.luq - INFO - Split text (5423 chars) into 38 sentences
2025-08-15 11:56:40,014 - uq_methods.implementations.luq - INFO - Response 18 split into 38 sentences
2025-08-15 11:56:40,017 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:40,017 - uq_methods.implementations.luq - INFO - Split text (6157 chars) into 37 sentences
2025-08-15 11:56:40,017 - uq_methods.implementations.luq - INFO - Response 19 split into 37 sentences
2025-08-15 11:56:40,019 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:40,019 - uq_methods.implementations.luq - INFO - Split text (5647 chars) into 41 sentences
2025-08-15 11:56:40,019 - uq_methods.implementations.luq - INFO - Response 20 split into 41 sentences
2025-08-15 11:56:40,022 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:40,022 - uq_methods.implementations.luq - INFO - Split text (5527 chars) into 36 sentences
2025-08-15 11:56:40,022 - uq_methods.implementations.luq - INFO - Response 21 split into 36 sentences
2025-08-15 11:56:40,025 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:40,025 - uq_methods.implementations.luq - INFO - Split text (6011 chars) into 42 sentences
2025-08-15 11:56:40,025 - uq_methods.implementations.luq - INFO - Response 22 split into 42 sentences
2025-08-15 11:56:40,028 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:40,028 - uq_methods.implementations.luq - INFO - Split text (5658 chars) into 40 sentences
2025-08-15 11:56:40,028 - uq_methods.implementations.luq - INFO - Response 23 split into 40 sentences
2025-08-15 11:56:40,030 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:56:40,030 - uq_methods.implementations.luq - INFO - Split text (5395 chars) into 38 sentences
2025-08-15 11:56:40,030 - uq_methods.implementations.luq - INFO - Response 24 split into 38 sentences
2025-08-15 11:56:40,033 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:40,033 - uq_methods.implementations.luq - INFO - Split text (6789 chars) into 50 sentences
2025-08-15 11:56:40,033 - uq_methods.implementations.luq - INFO - Response 25 split into 50 sentences
2025-08-15 11:56:40,036 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 59 sentences
2025-08-15 11:56:40,036 - uq_methods.implementations.luq - INFO - Split text (7909 chars) into 59 sentences
2025-08-15 11:56:40,036 - uq_methods.implementations.luq - INFO - Response 26 split into 59 sentences
2025-08-15 11:56:40,039 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:40,039 - uq_methods.implementations.luq - INFO - Split text (6581 chars) into 45 sentences
2025-08-15 11:56:40,039 - uq_methods.implementations.luq - INFO - Response 27 split into 45 sentences
2025-08-15 11:56:40,042 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:40,042 - uq_methods.implementations.luq - INFO - Split text (6876 chars) into 48 sentences
2025-08-15 11:56:40,042 - uq_methods.implementations.luq - INFO - Response 28 split into 48 sentences
2025-08-15 11:56:40,045 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:40,045 - uq_methods.implementations.luq - INFO - Split text (6753 chars) into 48 sentences
2025-08-15 11:56:40,045 - uq_methods.implementations.luq - INFO - Response 29 split into 48 sentences
2025-08-15 11:56:40,047 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:40,047 - uq_methods.implementations.luq - INFO - Split text (5923 chars) into 46 sentences
2025-08-15 11:56:40,048 - uq_methods.implementations.luq - INFO - Response 30 split into 46 sentences
2025-08-15 11:56:40,048 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:56:40,149 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:56:40,243 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:56:40,319 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:56:40,417 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:56:40,495 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:56:40,579 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:56:40,660 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:56:40,756 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:56:40,854 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:56:40,967 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:56:41,026 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:56:41,112 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:56:41,205 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:56:41,276 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:56:41,379 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:56:41,487 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:56:41,588 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:56:41,678 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:56:41,765 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:56:41,861 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:56:41,946 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:56:42,045 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:56:42,140 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:56:42,230 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:56:42,347 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:56:42,485 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:56:42,591 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:56:42,705 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:56:42,819 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:56:42,958 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:56:42,961 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:42,961 - uq_methods.implementations.luq - INFO - Split text (5398 chars) into 40 sentences
2025-08-15 11:56:42,961 - uq_methods.implementations.luq - INFO - Response 1 split into 40 sentences
2025-08-15 11:56:42,963 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:42,963 - uq_methods.implementations.luq - INFO - Split text (5067 chars) into 39 sentences
2025-08-15 11:56:42,963 - uq_methods.implementations.luq - INFO - Response 2 split into 39 sentences
2025-08-15 11:56:42,965 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:56:42,965 - uq_methods.implementations.luq - INFO - Split text (4580 chars) into 33 sentences
2025-08-15 11:56:42,965 - uq_methods.implementations.luq - INFO - Response 3 split into 33 sentences
2025-08-15 11:56:42,968 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:42,968 - uq_methods.implementations.luq - INFO - Split text (6027 chars) into 42 sentences
2025-08-15 11:56:42,968 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:56:42,970 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:42,970 - uq_methods.implementations.luq - INFO - Split text (4980 chars) into 35 sentences
2025-08-15 11:56:42,970 - uq_methods.implementations.luq - INFO - Response 5 split into 35 sentences
2025-08-15 11:56:42,972 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:42,972 - uq_methods.implementations.luq - INFO - Split text (5575 chars) into 41 sentences
2025-08-15 11:56:42,972 - uq_methods.implementations.luq - INFO - Response 6 split into 41 sentences
2025-08-15 11:56:42,975 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:42,975 - uq_methods.implementations.luq - INFO - Split text (6138 chars) into 43 sentences
2025-08-15 11:56:42,975 - uq_methods.implementations.luq - INFO - Response 7 split into 43 sentences
2025-08-15 11:56:42,978 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:42,978 - uq_methods.implementations.luq - INFO - Split text (6654 chars) into 49 sentences
2025-08-15 11:56:42,978 - uq_methods.implementations.luq - INFO - Response 8 split into 49 sentences
2025-08-15 11:56:42,980 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:42,980 - uq_methods.implementations.luq - INFO - Split text (6597 chars) into 42 sentences
2025-08-15 11:56:42,980 - uq_methods.implementations.luq - INFO - Response 9 split into 42 sentences
2025-08-15 11:56:42,983 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:42,983 - uq_methods.implementations.luq - INFO - Split text (6866 chars) into 47 sentences
2025-08-15 11:56:42,983 - uq_methods.implementations.luq - INFO - Response 10 split into 47 sentences
2025-08-15 11:56:42,986 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:56:42,986 - uq_methods.implementations.luq - INFO - Split text (6706 chars) into 52 sentences
2025-08-15 11:56:42,986 - uq_methods.implementations.luq - INFO - Response 11 split into 52 sentences
2025-08-15 11:56:42,989 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:42,989 - uq_methods.implementations.luq - INFO - Split text (7344 chars) into 49 sentences
2025-08-15 11:56:42,989 - uq_methods.implementations.luq - INFO - Response 12 split into 49 sentences
2025-08-15 11:56:42,991 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:42,991 - uq_methods.implementations.luq - INFO - Split text (5029 chars) into 35 sentences
2025-08-15 11:56:42,991 - uq_methods.implementations.luq - INFO - Response 13 split into 35 sentences
2025-08-15 11:56:42,994 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:42,994 - uq_methods.implementations.luq - INFO - Split text (6388 chars) into 40 sentences
2025-08-15 11:56:42,994 - uq_methods.implementations.luq - INFO - Response 14 split into 40 sentences
2025-08-15 11:56:42,996 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:42,996 - uq_methods.implementations.luq - INFO - Split text (6336 chars) into 41 sentences
2025-08-15 11:56:42,997 - uq_methods.implementations.luq - INFO - Response 15 split into 41 sentences
2025-08-15 11:56:42,999 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:42,999 - uq_methods.implementations.luq - INFO - Split text (4878 chars) into 36 sentences
2025-08-15 11:56:42,999 - uq_methods.implementations.luq - INFO - Response 16 split into 36 sentences
2025-08-15 11:56:43,002 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:43,002 - uq_methods.implementations.luq - INFO - Split text (6130 chars) into 45 sentences
2025-08-15 11:56:43,002 - uq_methods.implementations.luq - INFO - Response 17 split into 45 sentences
2025-08-15 11:56:43,004 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:43,004 - uq_methods.implementations.luq - INFO - Split text (5738 chars) into 37 sentences
2025-08-15 11:56:43,004 - uq_methods.implementations.luq - INFO - Response 18 split into 37 sentences
2025-08-15 11:56:43,007 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:43,007 - uq_methods.implementations.luq - INFO - Split text (6304 chars) into 46 sentences
2025-08-15 11:56:43,007 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:56:43,009 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:43,009 - uq_methods.implementations.luq - INFO - Split text (6156 chars) into 43 sentences
2025-08-15 11:56:43,009 - uq_methods.implementations.luq - INFO - Response 20 split into 43 sentences
2025-08-15 11:56:43,012 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:43,012 - uq_methods.implementations.luq - INFO - Split text (6170 chars) into 48 sentences
2025-08-15 11:56:43,012 - uq_methods.implementations.luq - INFO - Response 21 split into 48 sentences
2025-08-15 11:56:43,014 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:43,014 - uq_methods.implementations.luq - INFO - Split text (6138 chars) into 40 sentences
2025-08-15 11:56:43,014 - uq_methods.implementations.luq - INFO - Response 22 split into 40 sentences
2025-08-15 11:56:43,017 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:43,017 - uq_methods.implementations.luq - INFO - Split text (6625 chars) into 45 sentences
2025-08-15 11:56:43,017 - uq_methods.implementations.luq - INFO - Response 23 split into 45 sentences
2025-08-15 11:56:43,020 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:43,020 - uq_methods.implementations.luq - INFO - Split text (6489 chars) into 41 sentences
2025-08-15 11:56:43,020 - uq_methods.implementations.luq - INFO - Response 24 split into 41 sentences
2025-08-15 11:56:43,022 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 28 sentences
2025-08-15 11:56:43,022 - uq_methods.implementations.luq - INFO - Split text (4715 chars) into 28 sentences
2025-08-15 11:56:43,022 - uq_methods.implementations.luq - INFO - Response 25 split into 28 sentences
2025-08-15 11:56:43,024 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:43,024 - uq_methods.implementations.luq - INFO - Split text (5319 chars) into 37 sentences
2025-08-15 11:56:43,024 - uq_methods.implementations.luq - INFO - Response 26 split into 37 sentences
2025-08-15 11:56:43,026 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:43,026 - uq_methods.implementations.luq - INFO - Split text (5178 chars) into 35 sentences
2025-08-15 11:56:43,026 - uq_methods.implementations.luq - INFO - Response 27 split into 35 sentences
2025-08-15 11:56:43,028 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:43,029 - uq_methods.implementations.luq - INFO - Split text (4807 chars) into 35 sentences
2025-08-15 11:56:43,029 - uq_methods.implementations.luq - INFO - Response 28 split into 35 sentences
2025-08-15 11:56:43,031 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:56:43,031 - uq_methods.implementations.luq - INFO - Split text (5120 chars) into 33 sentences
2025-08-15 11:56:43,031 - uq_methods.implementations.luq - INFO - Response 29 split into 33 sentences
2025-08-15 11:56:43,033 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:43,033 - uq_methods.implementations.luq - INFO - Split text (5051 chars) into 36 sentences
2025-08-15 11:56:43,033 - uq_methods.implementations.luq - INFO - Response 30 split into 36 sentences
2025-08-15 11:56:43,033 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:56:43,126 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:56:43,217 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:56:43,294 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:56:43,391 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:56:43,472 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:56:43,566 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:56:43,666 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:56:43,778 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:56:43,875 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:56:43,983 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:56:44,102 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:56:44,215 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:56:44,296 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:56:44,388 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:56:44,482 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:56:44,566 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:56:44,670 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:56:44,756 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:56:44,862 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:56:44,962 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:56:45,073 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:56:45,166 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:56:45,270 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:56:45,365 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:56:45,430 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:56:45,516 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:56:45,597 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:56:45,679 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:56:45,756 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:56:45,871 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:56:45,873 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:45,873 - uq_methods.implementations.luq - INFO - Split text (4949 chars) into 43 sentences
2025-08-15 11:56:45,873 - uq_methods.implementations.luq - INFO - Response 1 split into 43 sentences
2025-08-15 11:56:45,875 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:45,875 - uq_methods.implementations.luq - INFO - Split text (4995 chars) into 41 sentences
2025-08-15 11:56:45,876 - uq_methods.implementations.luq - INFO - Response 2 split into 41 sentences
2025-08-15 11:56:45,878 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:45,878 - uq_methods.implementations.luq - INFO - Split text (6059 chars) into 51 sentences
2025-08-15 11:56:45,878 - uq_methods.implementations.luq - INFO - Response 3 split into 51 sentences
2025-08-15 11:56:45,880 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:45,880 - uq_methods.implementations.luq - INFO - Split text (5368 chars) into 35 sentences
2025-08-15 11:56:45,880 - uq_methods.implementations.luq - INFO - Response 4 split into 35 sentences
2025-08-15 11:56:45,883 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:45,883 - uq_methods.implementations.luq - INFO - Split text (5116 chars) into 37 sentences
2025-08-15 11:56:45,883 - uq_methods.implementations.luq - INFO - Response 5 split into 37 sentences
2025-08-15 11:56:45,885 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:45,885 - uq_methods.implementations.luq - INFO - Split text (4997 chars) into 41 sentences
2025-08-15 11:56:45,885 - uq_methods.implementations.luq - INFO - Response 6 split into 41 sentences
2025-08-15 11:56:45,887 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:45,887 - uq_methods.implementations.luq - INFO - Split text (6277 chars) into 42 sentences
2025-08-15 11:56:45,887 - uq_methods.implementations.luq - INFO - Response 7 split into 42 sentences
2025-08-15 11:56:45,890 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:45,890 - uq_methods.implementations.luq - INFO - Split text (6459 chars) into 47 sentences
2025-08-15 11:56:45,890 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:56:45,893 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:45,893 - uq_methods.implementations.luq - INFO - Split text (6549 chars) into 46 sentences
2025-08-15 11:56:45,893 - uq_methods.implementations.luq - INFO - Response 9 split into 46 sentences
2025-08-15 11:56:45,895 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:45,895 - uq_methods.implementations.luq - INFO - Split text (6125 chars) into 49 sentences
2025-08-15 11:56:45,895 - uq_methods.implementations.luq - INFO - Response 10 split into 49 sentences
2025-08-15 11:56:45,898 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:45,898 - uq_methods.implementations.luq - INFO - Split text (6866 chars) into 51 sentences
2025-08-15 11:56:45,898 - uq_methods.implementations.luq - INFO - Response 11 split into 51 sentences
2025-08-15 11:56:45,901 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:45,901 - uq_methods.implementations.luq - INFO - Split text (6826 chars) into 46 sentences
2025-08-15 11:56:45,901 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:56:45,904 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:45,904 - uq_methods.implementations.luq - INFO - Split text (6402 chars) into 51 sentences
2025-08-15 11:56:45,904 - uq_methods.implementations.luq - INFO - Response 13 split into 51 sentences
2025-08-15 11:56:45,906 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:56:45,906 - uq_methods.implementations.luq - INFO - Split text (7118 chars) into 53 sentences
2025-08-15 11:56:45,906 - uq_methods.implementations.luq - INFO - Response 14 split into 53 sentences
2025-08-15 11:56:45,909 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:56:45,909 - uq_methods.implementations.luq - INFO - Split text (6824 chars) into 52 sentences
2025-08-15 11:56:45,909 - uq_methods.implementations.luq - INFO - Response 15 split into 52 sentences
2025-08-15 11:56:45,912 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:45,912 - uq_methods.implementations.luq - INFO - Split text (6531 chars) into 51 sentences
2025-08-15 11:56:45,912 - uq_methods.implementations.luq - INFO - Response 16 split into 51 sentences
2025-08-15 11:56:45,915 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:45,915 - uq_methods.implementations.luq - INFO - Split text (6962 chars) into 49 sentences
2025-08-15 11:56:45,915 - uq_methods.implementations.luq - INFO - Response 17 split into 49 sentences
2025-08-15 11:56:45,918 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:56:45,918 - uq_methods.implementations.luq - INFO - Split text (6769 chars) into 56 sentences
2025-08-15 11:56:45,918 - uq_methods.implementations.luq - INFO - Response 18 split into 56 sentences
2025-08-15 11:56:45,920 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:45,920 - uq_methods.implementations.luq - INFO - Split text (5366 chars) into 43 sentences
2025-08-15 11:56:45,920 - uq_methods.implementations.luq - INFO - Response 19 split into 43 sentences
2025-08-15 11:56:45,923 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 64 sentences
2025-08-15 11:56:45,923 - uq_methods.implementations.luq - INFO - Split text (7023 chars) into 64 sentences
2025-08-15 11:56:45,923 - uq_methods.implementations.luq - INFO - Response 20 split into 64 sentences
2025-08-15 11:56:45,925 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:45,925 - uq_methods.implementations.luq - INFO - Split text (5725 chars) into 51 sentences
2025-08-15 11:56:45,925 - uq_methods.implementations.luq - INFO - Response 21 split into 51 sentences
2025-08-15 11:56:45,928 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:45,928 - uq_methods.implementations.luq - INFO - Split text (5140 chars) into 37 sentences
2025-08-15 11:56:45,928 - uq_methods.implementations.luq - INFO - Response 22 split into 37 sentences
2025-08-15 11:56:45,930 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:45,930 - uq_methods.implementations.luq - INFO - Split text (5671 chars) into 50 sentences
2025-08-15 11:56:45,930 - uq_methods.implementations.luq - INFO - Response 23 split into 50 sentences
2025-08-15 11:56:45,933 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:45,933 - uq_methods.implementations.luq - INFO - Split text (6323 chars) into 46 sentences
2025-08-15 11:56:45,933 - uq_methods.implementations.luq - INFO - Response 24 split into 46 sentences
2025-08-15 11:56:45,935 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:45,935 - uq_methods.implementations.luq - INFO - Split text (5368 chars) into 37 sentences
2025-08-15 11:56:45,935 - uq_methods.implementations.luq - INFO - Response 25 split into 37 sentences
2025-08-15 11:56:45,937 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:45,937 - uq_methods.implementations.luq - INFO - Split text (6149 chars) into 42 sentences
2025-08-15 11:56:45,937 - uq_methods.implementations.luq - INFO - Response 26 split into 42 sentences
2025-08-15 11:56:45,940 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:45,940 - uq_methods.implementations.luq - INFO - Split text (7076 chars) into 47 sentences
2025-08-15 11:56:45,940 - uq_methods.implementations.luq - INFO - Response 27 split into 47 sentences
2025-08-15 11:56:45,943 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:45,943 - uq_methods.implementations.luq - INFO - Split text (6945 chars) into 43 sentences
2025-08-15 11:56:45,943 - uq_methods.implementations.luq - INFO - Response 28 split into 43 sentences
2025-08-15 11:56:45,946 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:45,946 - uq_methods.implementations.luq - INFO - Split text (6660 chars) into 50 sentences
2025-08-15 11:56:45,946 - uq_methods.implementations.luq - INFO - Response 29 split into 50 sentences
2025-08-15 11:56:45,948 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:45,948 - uq_methods.implementations.luq - INFO - Split text (6045 chars) into 43 sentences
2025-08-15 11:56:45,948 - uq_methods.implementations.luq - INFO - Response 30 split into 43 sentences
2025-08-15 11:56:45,948 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:56:46,050 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:56:46,146 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:56:46,265 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:56:46,347 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:56:46,434 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:56:46,530 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:56:46,628 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:56:46,737 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:56:46,844 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:56:46,958 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:56:47,078 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:56:47,184 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:56:47,303 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:56:47,426 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:56:47,548 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:56:47,667 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:56:47,782 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:56:47,913 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:56:48,014 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:56:48,164 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:56:48,283 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:56:48,370 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:56:48,486 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:56:48,594 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:56:48,681 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:56:48,780 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:56:48,890 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:56:48,990 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:56:49,107 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:56:49,244 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:56:49,247 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:56:49,247 - uq_methods.implementations.luq - INFO - Split text (4893 chars) into 35 sentences
2025-08-15 11:56:49,247 - uq_methods.implementations.luq - INFO - Response 1 split into 35 sentences
2025-08-15 11:56:49,248 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:56:49,248 - uq_methods.implementations.luq - INFO - Split text (4087 chars) into 31 sentences
2025-08-15 11:56:49,248 - uq_methods.implementations.luq - INFO - Response 2 split into 31 sentences
2025-08-15 11:56:49,251 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:49,251 - uq_methods.implementations.luq - INFO - Split text (5044 chars) into 37 sentences
2025-08-15 11:56:49,251 - uq_methods.implementations.luq - INFO - Response 3 split into 37 sentences
2025-08-15 11:56:49,252 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:56:49,252 - uq_methods.implementations.luq - INFO - Split text (4308 chars) into 30 sentences
2025-08-15 11:56:49,253 - uq_methods.implementations.luq - INFO - Response 4 split into 30 sentences
2025-08-15 11:56:49,255 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:49,255 - uq_methods.implementations.luq - INFO - Split text (4822 chars) into 37 sentences
2025-08-15 11:56:49,255 - uq_methods.implementations.luq - INFO - Response 5 split into 37 sentences
2025-08-15 11:56:49,257 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:49,257 - uq_methods.implementations.luq - INFO - Split text (5108 chars) into 36 sentences
2025-08-15 11:56:49,257 - uq_methods.implementations.luq - INFO - Response 6 split into 36 sentences
2025-08-15 11:56:49,259 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:49,259 - uq_methods.implementations.luq - INFO - Split text (6156 chars) into 41 sentences
2025-08-15 11:56:49,259 - uq_methods.implementations.luq - INFO - Response 7 split into 41 sentences
2025-08-15 11:56:49,262 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:49,262 - uq_methods.implementations.luq - INFO - Split text (5870 chars) into 47 sentences
2025-08-15 11:56:49,262 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:56:49,264 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:49,264 - uq_methods.implementations.luq - INFO - Split text (6435 chars) into 44 sentences
2025-08-15 11:56:49,264 - uq_methods.implementations.luq - INFO - Response 9 split into 44 sentences
2025-08-15 11:56:49,267 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:49,267 - uq_methods.implementations.luq - INFO - Split text (5596 chars) into 41 sentences
2025-08-15 11:56:49,267 - uq_methods.implementations.luq - INFO - Response 10 split into 41 sentences
2025-08-15 11:56:49,269 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:49,269 - uq_methods.implementations.luq - INFO - Split text (5196 chars) into 43 sentences
2025-08-15 11:56:49,269 - uq_methods.implementations.luq - INFO - Response 11 split into 43 sentences
2025-08-15 11:56:49,271 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:49,271 - uq_methods.implementations.luq - INFO - Split text (5944 chars) into 46 sentences
2025-08-15 11:56:49,271 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:56:49,274 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:49,274 - uq_methods.implementations.luq - INFO - Split text (5437 chars) into 37 sentences
2025-08-15 11:56:49,274 - uq_methods.implementations.luq - INFO - Response 13 split into 37 sentences
2025-08-15 11:56:49,276 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:49,276 - uq_methods.implementations.luq - INFO - Split text (5063 chars) into 37 sentences
2025-08-15 11:56:49,276 - uq_methods.implementations.luq - INFO - Response 14 split into 37 sentences
2025-08-15 11:56:49,278 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:49,278 - uq_methods.implementations.luq - INFO - Split text (5185 chars) into 40 sentences
2025-08-15 11:56:49,278 - uq_methods.implementations.luq - INFO - Response 15 split into 40 sentences
2025-08-15 11:56:49,280 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:49,280 - uq_methods.implementations.luq - INFO - Split text (5380 chars) into 39 sentences
2025-08-15 11:56:49,280 - uq_methods.implementations.luq - INFO - Response 16 split into 39 sentences
2025-08-15 11:56:49,283 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:49,283 - uq_methods.implementations.luq - INFO - Split text (5351 chars) into 36 sentences
2025-08-15 11:56:49,283 - uq_methods.implementations.luq - INFO - Response 17 split into 36 sentences
2025-08-15 11:56:49,285 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:49,285 - uq_methods.implementations.luq - INFO - Split text (5606 chars) into 44 sentences
2025-08-15 11:56:49,285 - uq_methods.implementations.luq - INFO - Response 18 split into 44 sentences
2025-08-15 11:56:49,288 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:49,288 - uq_methods.implementations.luq - INFO - Split text (6064 chars) into 44 sentences
2025-08-15 11:56:49,288 - uq_methods.implementations.luq - INFO - Response 19 split into 44 sentences
2025-08-15 11:56:49,290 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:49,290 - uq_methods.implementations.luq - INFO - Split text (6740 chars) into 51 sentences
2025-08-15 11:56:49,290 - uq_methods.implementations.luq - INFO - Response 20 split into 51 sentences
2025-08-15 11:56:49,293 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:49,293 - uq_methods.implementations.luq - INFO - Split text (5832 chars) into 43 sentences
2025-08-15 11:56:49,293 - uq_methods.implementations.luq - INFO - Response 21 split into 43 sentences
2025-08-15 11:56:49,295 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:49,295 - uq_methods.implementations.luq - INFO - Split text (5767 chars) into 41 sentences
2025-08-15 11:56:49,295 - uq_methods.implementations.luq - INFO - Response 22 split into 41 sentences
2025-08-15 11:56:49,298 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:49,298 - uq_methods.implementations.luq - INFO - Split text (6228 chars) into 49 sentences
2025-08-15 11:56:49,298 - uq_methods.implementations.luq - INFO - Response 23 split into 49 sentences
2025-08-15 11:56:49,301 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:49,301 - uq_methods.implementations.luq - INFO - Split text (6727 chars) into 49 sentences
2025-08-15 11:56:49,301 - uq_methods.implementations.luq - INFO - Response 24 split into 49 sentences
2025-08-15 11:56:49,302 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 27 sentences
2025-08-15 11:56:49,302 - uq_methods.implementations.luq - INFO - Split text (3771 chars) into 27 sentences
2025-08-15 11:56:49,302 - uq_methods.implementations.luq - INFO - Response 25 split into 27 sentences
2025-08-15 11:56:49,304 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:56:49,304 - uq_methods.implementations.luq - INFO - Split text (4275 chars) into 25 sentences
2025-08-15 11:56:49,304 - uq_methods.implementations.luq - INFO - Response 26 split into 25 sentences
2025-08-15 11:56:49,306 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 25 sentences
2025-08-15 11:56:49,306 - uq_methods.implementations.luq - INFO - Split text (4183 chars) into 25 sentences
2025-08-15 11:56:49,306 - uq_methods.implementations.luq - INFO - Response 27 split into 25 sentences
2025-08-15 11:56:49,308 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 28 sentences
2025-08-15 11:56:49,308 - uq_methods.implementations.luq - INFO - Split text (4598 chars) into 28 sentences
2025-08-15 11:56:49,308 - uq_methods.implementations.luq - INFO - Response 28 split into 28 sentences
2025-08-15 11:56:49,310 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:56:49,310 - uq_methods.implementations.luq - INFO - Split text (4622 chars) into 31 sentences
2025-08-15 11:56:49,310 - uq_methods.implementations.luq - INFO - Response 29 split into 31 sentences
2025-08-15 11:56:49,312 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:56:49,312 - uq_methods.implementations.luq - INFO - Split text (4577 chars) into 30 sentences
2025-08-15 11:56:49,312 - uq_methods.implementations.luq - INFO - Response 30 split into 30 sentences
2025-08-15 11:56:49,312 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:56:49,393 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:56:49,463 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:56:49,547 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:56:49,614 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:56:49,698 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:56:49,779 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:56:49,872 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:56:49,979 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:56:50,079 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:56:50,174 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:56:50,271 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:56:50,376 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:56:50,459 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:56:50,543 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:56:50,635 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:56:50,725 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:56:50,808 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:56:50,908 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:56:51,008 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:56:51,124 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:56:51,222 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:56:51,316 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:56:51,429 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:56:51,540 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:56:51,602 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:56:51,659 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:56:51,717 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:56:51,781 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:56:51,852 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:56:51,951 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 44 responses
2025-08-15 11:56:51,954 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:51,954 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 42 sentences
2025-08-15 11:56:51,954 - uq_methods.implementations.luq - INFO - Response 1 split into 42 sentences
2025-08-15 11:56:51,956 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:51,956 - uq_methods.implementations.luq - INFO - Split text (5720 chars) into 46 sentences
2025-08-15 11:56:51,956 - uq_methods.implementations.luq - INFO - Response 2 split into 46 sentences
2025-08-15 11:56:51,959 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:51,959 - uq_methods.implementations.luq - INFO - Split text (6189 chars) into 44 sentences
2025-08-15 11:56:51,959 - uq_methods.implementations.luq - INFO - Response 3 split into 44 sentences
2025-08-15 11:56:51,961 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:56:51,961 - uq_methods.implementations.luq - INFO - Split text (4949 chars) into 38 sentences
2025-08-15 11:56:51,961 - uq_methods.implementations.luq - INFO - Response 4 split into 38 sentences
2025-08-15 11:56:51,963 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:51,963 - uq_methods.implementations.luq - INFO - Split text (5286 chars) into 42 sentences
2025-08-15 11:56:51,963 - uq_methods.implementations.luq - INFO - Response 5 split into 42 sentences
2025-08-15 11:56:51,966 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:51,966 - uq_methods.implementations.luq - INFO - Split text (5492 chars) into 42 sentences
2025-08-15 11:56:51,966 - uq_methods.implementations.luq - INFO - Response 6 split into 42 sentences
2025-08-15 11:56:51,968 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 34 sentences
2025-08-15 11:56:51,968 - uq_methods.implementations.luq - INFO - Split text (5483 chars) into 34 sentences
2025-08-15 11:56:51,968 - uq_methods.implementations.luq - INFO - Response 7 split into 34 sentences
2025-08-15 11:56:51,971 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:51,971 - uq_methods.implementations.luq - INFO - Split text (6434 chars) into 46 sentences
2025-08-15 11:56:51,971 - uq_methods.implementations.luq - INFO - Response 8 split into 46 sentences
2025-08-15 11:56:51,973 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:56:51,973 - uq_methods.implementations.luq - INFO - Split text (6074 chars) into 53 sentences
2025-08-15 11:56:51,973 - uq_methods.implementations.luq - INFO - Response 9 split into 53 sentences
2025-08-15 11:56:51,976 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:51,976 - uq_methods.implementations.luq - INFO - Split text (5879 chars) into 43 sentences
2025-08-15 11:56:51,976 - uq_methods.implementations.luq - INFO - Response 10 split into 43 sentences
2025-08-15 11:56:51,978 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:51,978 - uq_methods.implementations.luq - INFO - Split text (5483 chars) into 45 sentences
2025-08-15 11:56:51,978 - uq_methods.implementations.luq - INFO - Response 11 split into 45 sentences
2025-08-15 11:56:51,981 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:51,981 - uq_methods.implementations.luq - INFO - Split text (6322 chars) into 45 sentences
2025-08-15 11:56:51,981 - uq_methods.implementations.luq - INFO - Response 12 split into 45 sentences
2025-08-15 11:56:51,983 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:51,983 - uq_methods.implementations.luq - INFO - Split text (5584 chars) into 40 sentences
2025-08-15 11:56:51,983 - uq_methods.implementations.luq - INFO - Response 13 split into 40 sentences
2025-08-15 11:56:51,986 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 57 sentences
2025-08-15 11:56:51,986 - uq_methods.implementations.luq - INFO - Split text (7829 chars) into 57 sentences
2025-08-15 11:56:51,986 - uq_methods.implementations.luq - INFO - Response 14 split into 57 sentences
2025-08-15 11:56:51,989 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:51,989 - uq_methods.implementations.luq - INFO - Split text (5537 chars) into 47 sentences
2025-08-15 11:56:51,989 - uq_methods.implementations.luq - INFO - Response 15 split into 47 sentences
2025-08-15 11:56:51,991 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:51,991 - uq_methods.implementations.luq - INFO - Split text (5785 chars) into 47 sentences
2025-08-15 11:56:51,991 - uq_methods.implementations.luq - INFO - Response 16 split into 47 sentences
2025-08-15 11:56:51,993 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:51,993 - uq_methods.implementations.luq - INFO - Split text (5091 chars) into 39 sentences
2025-08-15 11:56:51,993 - uq_methods.implementations.luq - INFO - Response 17 split into 39 sentences
2025-08-15 11:56:51,996 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:51,996 - uq_methods.implementations.luq - INFO - Split text (5437 chars) into 39 sentences
2025-08-15 11:56:51,996 - uq_methods.implementations.luq - INFO - Response 18 split into 39 sentences
2025-08-15 11:56:51,998 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:51,998 - uq_methods.implementations.luq - INFO - Split text (5741 chars) into 46 sentences
2025-08-15 11:56:51,998 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:56:52,000 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:52,000 - uq_methods.implementations.luq - INFO - Split text (5374 chars) into 39 sentences
2025-08-15 11:56:52,000 - uq_methods.implementations.luq - INFO - Response 20 split into 39 sentences
2025-08-15 11:56:52,003 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:52,003 - uq_methods.implementations.luq - INFO - Split text (6463 chars) into 47 sentences
2025-08-15 11:56:52,003 - uq_methods.implementations.luq - INFO - Response 21 split into 47 sentences
2025-08-15 11:56:52,006 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:52,006 - uq_methods.implementations.luq - INFO - Split text (5563 chars) into 44 sentences
2025-08-15 11:56:52,006 - uq_methods.implementations.luq - INFO - Response 22 split into 44 sentences
2025-08-15 11:56:52,008 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:52,008 - uq_methods.implementations.luq - INFO - Split text (5999 chars) into 39 sentences
2025-08-15 11:56:52,008 - uq_methods.implementations.luq - INFO - Response 23 split into 39 sentences
2025-08-15 11:56:52,011 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:52,011 - uq_methods.implementations.luq - INFO - Split text (5656 chars) into 46 sentences
2025-08-15 11:56:52,011 - uq_methods.implementations.luq - INFO - Response 24 split into 46 sentences
2025-08-15 11:56:52,013 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:52,013 - uq_methods.implementations.luq - INFO - Split text (5906 chars) into 46 sentences
2025-08-15 11:56:52,013 - uq_methods.implementations.luq - INFO - Response 25 split into 46 sentences
2025-08-15 11:56:52,015 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:52,015 - uq_methods.implementations.luq - INFO - Split text (4561 chars) into 37 sentences
2025-08-15 11:56:52,015 - uq_methods.implementations.luq - INFO - Response 26 split into 37 sentences
2025-08-15 11:56:52,018 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 58 sentences
2025-08-15 11:56:52,018 - uq_methods.implementations.luq - INFO - Split text (6901 chars) into 58 sentences
2025-08-15 11:56:52,018 - uq_methods.implementations.luq - INFO - Response 27 split into 58 sentences
2025-08-15 11:56:52,021 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:52,021 - uq_methods.implementations.luq - INFO - Split text (6620 chars) into 51 sentences
2025-08-15 11:56:52,021 - uq_methods.implementations.luq - INFO - Response 28 split into 51 sentences
2025-08-15 11:56:52,023 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:52,023 - uq_methods.implementations.luq - INFO - Split text (5521 chars) into 43 sentences
2025-08-15 11:56:52,023 - uq_methods.implementations.luq - INFO - Response 29 split into 43 sentences
2025-08-15 11:56:52,025 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:56:52,025 - uq_methods.implementations.luq - INFO - Split text (4849 chars) into 39 sentences
2025-08-15 11:56:52,025 - uq_methods.implementations.luq - INFO - Response 30 split into 39 sentences
2025-08-15 11:56:52,027 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:52,027 - uq_methods.implementations.luq - INFO - Split text (5024 chars) into 41 sentences
2025-08-15 11:56:52,027 - uq_methods.implementations.luq - INFO - Response 31 split into 41 sentences
2025-08-15 11:56:52,030 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:52,030 - uq_methods.implementations.luq - INFO - Split text (5042 chars) into 40 sentences
2025-08-15 11:56:52,030 - uq_methods.implementations.luq - INFO - Response 32 split into 40 sentences
2025-08-15 11:56:52,032 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:52,032 - uq_methods.implementations.luq - INFO - Split text (6600 chars) into 49 sentences
2025-08-15 11:56:52,032 - uq_methods.implementations.luq - INFO - Response 33 split into 49 sentences
2025-08-15 11:56:52,035 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:56:52,035 - uq_methods.implementations.luq - INFO - Split text (7239 chars) into 52 sentences
2025-08-15 11:56:52,035 - uq_methods.implementations.luq - INFO - Response 34 split into 52 sentences
2025-08-15 11:56:52,038 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:52,038 - uq_methods.implementations.luq - INFO - Split text (7272 chars) into 50 sentences
2025-08-15 11:56:52,038 - uq_methods.implementations.luq - INFO - Response 35 split into 50 sentences
2025-08-15 11:56:52,041 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:52,041 - uq_methods.implementations.luq - INFO - Split text (6718 chars) into 47 sentences
2025-08-15 11:56:52,041 - uq_methods.implementations.luq - INFO - Response 36 split into 47 sentences
2025-08-15 11:56:52,044 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:52,044 - uq_methods.implementations.luq - INFO - Split text (7178 chars) into 48 sentences
2025-08-15 11:56:52,044 - uq_methods.implementations.luq - INFO - Response 37 split into 48 sentences
2025-08-15 11:56:52,047 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:56:52,047 - uq_methods.implementations.luq - INFO - Split text (7898 chars) into 54 sentences
2025-08-15 11:56:52,047 - uq_methods.implementations.luq - INFO - Response 38 split into 54 sentences
2025-08-15 11:56:52,050 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:52,050 - uq_methods.implementations.luq - INFO - Split text (7149 chars) into 51 sentences
2025-08-15 11:56:52,050 - uq_methods.implementations.luq - INFO - Response 39 split into 51 sentences
2025-08-15 11:56:52,053 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:56:52,053 - uq_methods.implementations.luq - INFO - Split text (6220 chars) into 45 sentences
2025-08-15 11:56:52,053 - uq_methods.implementations.luq - INFO - Response 40 split into 45 sentences
2025-08-15 11:56:52,056 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:52,056 - uq_methods.implementations.luq - INFO - Split text (6110 chars) into 43 sentences
2025-08-15 11:56:52,056 - uq_methods.implementations.luq - INFO - Response 41 split into 43 sentences
2025-08-15 11:56:52,058 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:52,058 - uq_methods.implementations.luq - INFO - Split text (6320 chars) into 48 sentences
2025-08-15 11:56:52,058 - uq_methods.implementations.luq - INFO - Response 42 split into 48 sentences
2025-08-15 11:56:52,061 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:56:52,061 - uq_methods.implementations.luq - INFO - Split text (7222 chars) into 52 sentences
2025-08-15 11:56:52,061 - uq_methods.implementations.luq - INFO - Response 43 split into 52 sentences
2025-08-15 11:56:52,064 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:52,064 - uq_methods.implementations.luq - INFO - Split text (6773 chars) into 49 sentences
2025-08-15 11:56:52,064 - uq_methods.implementations.luq - INFO - Response 44 split into 49 sentences
2025-08-15 11:56:52,064 - uq_methods.implementations.luq - INFO - Processing sample 1/44
2025-08-15 11:56:52,214 - uq_methods.implementations.luq - INFO - Processing sample 2/44
2025-08-15 11:56:52,377 - uq_methods.implementations.luq - INFO - Processing sample 3/44
2025-08-15 11:56:52,532 - uq_methods.implementations.luq - INFO - Processing sample 4/44
2025-08-15 11:56:52,666 - uq_methods.implementations.luq - INFO - Processing sample 5/44
2025-08-15 11:56:52,814 - uq_methods.implementations.luq - INFO - Processing sample 6/44
2025-08-15 11:56:52,962 - uq_methods.implementations.luq - INFO - Processing sample 7/44
2025-08-15 11:56:53,083 - uq_methods.implementations.luq - INFO - Processing sample 8/44
2025-08-15 11:56:53,245 - uq_methods.implementations.luq - INFO - Processing sample 9/44
2025-08-15 11:56:53,431 - uq_methods.implementations.luq - INFO - Processing sample 10/44
2025-08-15 11:56:53,582 - uq_methods.implementations.luq - INFO - Processing sample 11/44
2025-08-15 11:56:53,741 - uq_methods.implementations.luq - INFO - Processing sample 12/44
2025-08-15 11:56:53,899 - uq_methods.implementations.luq - INFO - Processing sample 13/44
2025-08-15 11:56:54,040 - uq_methods.implementations.luq - INFO - Processing sample 14/44
2025-08-15 11:56:54,242 - uq_methods.implementations.luq - INFO - Processing sample 15/44
2025-08-15 11:56:54,409 - uq_methods.implementations.luq - INFO - Processing sample 16/44
2025-08-15 11:56:54,574 - uq_methods.implementations.luq - INFO - Processing sample 17/44
2025-08-15 11:56:54,710 - uq_methods.implementations.luq - INFO - Processing sample 18/44
2025-08-15 11:56:54,848 - uq_methods.implementations.luq - INFO - Processing sample 19/44
2025-08-15 11:56:55,012 - uq_methods.implementations.luq - INFO - Processing sample 20/44
2025-08-15 11:56:55,149 - uq_methods.implementations.luq - INFO - Processing sample 21/44
2025-08-15 11:56:55,315 - uq_methods.implementations.luq - INFO - Processing sample 22/44
2025-08-15 11:56:55,471 - uq_methods.implementations.luq - INFO - Processing sample 23/44
2025-08-15 11:56:55,609 - uq_methods.implementations.luq - INFO - Processing sample 24/44
2025-08-15 11:56:55,771 - uq_methods.implementations.luq - INFO - Processing sample 25/44
2025-08-15 11:56:55,931 - uq_methods.implementations.luq - INFO - Processing sample 26/44
2025-08-15 11:56:56,062 - uq_methods.implementations.luq - INFO - Processing sample 27/44
2025-08-15 11:56:56,266 - uq_methods.implementations.luq - INFO - Processing sample 28/44
2025-08-15 11:56:56,447 - uq_methods.implementations.luq - INFO - Processing sample 29/44
2025-08-15 11:56:56,598 - uq_methods.implementations.luq - INFO - Processing sample 30/44
2025-08-15 11:56:56,736 - uq_methods.implementations.luq - INFO - Processing sample 31/44
2025-08-15 11:56:56,880 - uq_methods.implementations.luq - INFO - Processing sample 32/44
2025-08-15 11:56:57,021 - uq_methods.implementations.luq - INFO - Processing sample 33/44
2025-08-15 11:56:57,194 - uq_methods.implementations.luq - INFO - Processing sample 34/44
2025-08-15 11:56:57,379 - uq_methods.implementations.luq - INFO - Processing sample 35/44
2025-08-15 11:56:57,555 - uq_methods.implementations.luq - INFO - Processing sample 36/44
2025-08-15 11:56:57,722 - uq_methods.implementations.luq - INFO - Processing sample 37/44
2025-08-15 11:56:57,891 - uq_methods.implementations.luq - INFO - Processing sample 38/44
2025-08-15 11:56:58,083 - uq_methods.implementations.luq - INFO - Processing sample 39/44
2025-08-15 11:56:58,263 - uq_methods.implementations.luq - INFO - Processing sample 40/44
2025-08-15 11:56:58,421 - uq_methods.implementations.luq - INFO - Processing sample 41/44
2025-08-15 11:56:58,574 - uq_methods.implementations.luq - INFO - Processing sample 42/44
2025-08-15 11:56:58,745 - uq_methods.implementations.luq - INFO - Processing sample 43/44
2025-08-15 11:56:58,929 - uq_methods.implementations.luq - INFO - Processing sample 44/44
2025-08-15 11:56:59,188 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 28 responses
2025-08-15 11:56:59,191 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:56:59,191 - uq_methods.implementations.luq - INFO - Split text (7831 chars) into 54 sentences
2025-08-15 11:56:59,192 - uq_methods.implementations.luq - INFO - Response 1 split into 54 sentences
2025-08-15 11:56:59,194 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:56:59,194 - uq_methods.implementations.luq - INFO - Split text (7007 chars) into 53 sentences
2025-08-15 11:56:59,194 - uq_methods.implementations.luq - INFO - Response 2 split into 53 sentences
2025-08-15 11:56:59,197 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:56:59,197 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 38 sentences
2025-08-15 11:56:59,197 - uq_methods.implementations.luq - INFO - Response 3 split into 38 sentences
2025-08-15 11:56:59,200 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:59,200 - uq_methods.implementations.luq - INFO - Split text (6582 chars) into 44 sentences
2025-08-15 11:56:59,200 - uq_methods.implementations.luq - INFO - Response 4 split into 44 sentences
2025-08-15 11:56:59,203 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:59,203 - uq_methods.implementations.luq - INFO - Split text (6962 chars) into 48 sentences
2025-08-15 11:56:59,203 - uq_methods.implementations.luq - INFO - Response 5 split into 48 sentences
2025-08-15 11:56:59,205 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:59,205 - uq_methods.implementations.luq - INFO - Split text (6379 chars) into 46 sentences
2025-08-15 11:56:59,205 - uq_methods.implementations.luq - INFO - Response 6 split into 46 sentences
2025-08-15 11:56:59,208 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:59,208 - uq_methods.implementations.luq - INFO - Split text (6546 chars) into 50 sentences
2025-08-15 11:56:59,208 - uq_methods.implementations.luq - INFO - Response 7 split into 50 sentences
2025-08-15 11:56:59,211 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:56:59,211 - uq_methods.implementations.luq - INFO - Split text (6556 chars) into 47 sentences
2025-08-15 11:56:59,211 - uq_methods.implementations.luq - INFO - Response 8 split into 47 sentences
2025-08-15 11:56:59,213 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:56:59,213 - uq_methods.implementations.luq - INFO - Split text (6167 chars) into 49 sentences
2025-08-15 11:56:59,213 - uq_methods.implementations.luq - INFO - Response 9 split into 49 sentences
2025-08-15 11:56:59,216 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:59,216 - uq_methods.implementations.luq - INFO - Split text (6951 chars) into 48 sentences
2025-08-15 11:56:59,216 - uq_methods.implementations.luq - INFO - Response 10 split into 48 sentences
2025-08-15 11:56:59,219 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:59,219 - uq_methods.implementations.luq - INFO - Split text (6263 chars) into 43 sentences
2025-08-15 11:56:59,219 - uq_methods.implementations.luq - INFO - Response 11 split into 43 sentences
2025-08-15 11:56:59,221 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:56:59,221 - uq_methods.implementations.luq - INFO - Split text (6123 chars) into 42 sentences
2025-08-15 11:56:59,221 - uq_methods.implementations.luq - INFO - Response 12 split into 42 sentences
2025-08-15 11:56:59,224 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:56:59,224 - uq_methods.implementations.luq - INFO - Split text (7118 chars) into 54 sentences
2025-08-15 11:56:59,224 - uq_methods.implementations.luq - INFO - Response 13 split into 54 sentences
2025-08-15 11:56:59,227 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:56:59,227 - uq_methods.implementations.luq - INFO - Split text (6405 chars) into 50 sentences
2025-08-15 11:56:59,227 - uq_methods.implementations.luq - INFO - Response 14 split into 50 sentences
2025-08-15 11:56:59,230 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:59,230 - uq_methods.implementations.luq - INFO - Split text (6574 chars) into 48 sentences
2025-08-15 11:56:59,230 - uq_methods.implementations.luq - INFO - Response 15 split into 48 sentences
2025-08-15 11:56:59,233 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:56:59,233 - uq_methods.implementations.luq - INFO - Split text (7415 chars) into 52 sentences
2025-08-15 11:56:59,233 - uq_methods.implementations.luq - INFO - Response 16 split into 52 sentences
2025-08-15 11:56:59,235 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:56:59,235 - uq_methods.implementations.luq - INFO - Split text (5627 chars) into 37 sentences
2025-08-15 11:56:59,235 - uq_methods.implementations.luq - INFO - Response 17 split into 37 sentences
2025-08-15 11:56:59,237 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:59,237 - uq_methods.implementations.luq - INFO - Split text (5337 chars) into 44 sentences
2025-08-15 11:56:59,237 - uq_methods.implementations.luq - INFO - Response 18 split into 44 sentences
2025-08-15 11:56:59,240 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:56:59,240 - uq_methods.implementations.luq - INFO - Split text (6004 chars) into 46 sentences
2025-08-15 11:56:59,240 - uq_methods.implementations.luq - INFO - Response 19 split into 46 sentences
2025-08-15 11:56:59,242 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:59,242 - uq_methods.implementations.luq - INFO - Split text (5828 chars) into 43 sentences
2025-08-15 11:56:59,242 - uq_methods.implementations.luq - INFO - Response 20 split into 43 sentences
2025-08-15 11:56:59,245 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:59,245 - uq_methods.implementations.luq - INFO - Split text (6615 chars) into 48 sentences
2025-08-15 11:56:59,245 - uq_methods.implementations.luq - INFO - Response 21 split into 48 sentences
2025-08-15 11:56:59,248 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:56:59,248 - uq_methods.implementations.luq - INFO - Split text (5704 chars) into 43 sentences
2025-08-15 11:56:59,248 - uq_methods.implementations.luq - INFO - Response 22 split into 43 sentences
2025-08-15 11:56:59,250 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:56:59,250 - uq_methods.implementations.luq - INFO - Split text (5895 chars) into 41 sentences
2025-08-15 11:56:59,250 - uq_methods.implementations.luq - INFO - Response 23 split into 41 sentences
2025-08-15 11:56:59,252 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:56:59,252 - uq_methods.implementations.luq - INFO - Split text (5252 chars) into 36 sentences
2025-08-15 11:56:59,252 - uq_methods.implementations.luq - INFO - Response 24 split into 36 sentences
2025-08-15 11:56:59,255 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:56:59,255 - uq_methods.implementations.luq - INFO - Split text (6717 chars) into 51 sentences
2025-08-15 11:56:59,255 - uq_methods.implementations.luq - INFO - Response 25 split into 51 sentences
2025-08-15 11:56:59,258 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:56:59,258 - uq_methods.implementations.luq - INFO - Split text (6356 chars) into 40 sentences
2025-08-15 11:56:59,258 - uq_methods.implementations.luq - INFO - Response 26 split into 40 sentences
2025-08-15 11:56:59,260 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:56:59,260 - uq_methods.implementations.luq - INFO - Split text (5607 chars) into 44 sentences
2025-08-15 11:56:59,260 - uq_methods.implementations.luq - INFO - Response 27 split into 44 sentences
2025-08-15 11:56:59,263 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:56:59,263 - uq_methods.implementations.luq - INFO - Split text (6468 chars) into 48 sentences
2025-08-15 11:56:59,263 - uq_methods.implementations.luq - INFO - Response 28 split into 48 sentences
2025-08-15 11:56:59,263 - uq_methods.implementations.luq - INFO - Processing sample 1/28
2025-08-15 11:56:59,381 - uq_methods.implementations.luq - INFO - Processing sample 2/28
2025-08-15 11:56:59,498 - uq_methods.implementations.luq - INFO - Processing sample 3/28
2025-08-15 11:56:59,583 - uq_methods.implementations.luq - INFO - Processing sample 4/28
2025-08-15 11:56:59,678 - uq_methods.implementations.luq - INFO - Processing sample 5/28
2025-08-15 11:56:59,782 - uq_methods.implementations.luq - INFO - Processing sample 6/28
2025-08-15 11:56:59,882 - uq_methods.implementations.luq - INFO - Processing sample 7/28
2025-08-15 11:56:59,990 - uq_methods.implementations.luq - INFO - Processing sample 8/28
2025-08-15 11:57:00,094 - uq_methods.implementations.luq - INFO - Processing sample 9/28
2025-08-15 11:57:00,202 - uq_methods.implementations.luq - INFO - Processing sample 10/28
2025-08-15 11:57:00,306 - uq_methods.implementations.luq - INFO - Processing sample 11/28
2025-08-15 11:57:00,399 - uq_methods.implementations.luq - INFO - Processing sample 12/28
2025-08-15 11:57:00,490 - uq_methods.implementations.luq - INFO - Processing sample 13/28
2025-08-15 11:57:00,607 - uq_methods.implementations.luq - INFO - Processing sample 14/28
2025-08-15 11:57:00,716 - uq_methods.implementations.luq - INFO - Processing sample 15/28
2025-08-15 11:57:00,823 - uq_methods.implementations.luq - INFO - Processing sample 16/28
2025-08-15 11:57:00,936 - uq_methods.implementations.luq - INFO - Processing sample 17/28
2025-08-15 11:57:01,016 - uq_methods.implementations.luq - INFO - Processing sample 18/28
2025-08-15 11:57:01,114 - uq_methods.implementations.luq - INFO - Processing sample 19/28
2025-08-15 11:57:01,215 - uq_methods.implementations.luq - INFO - Processing sample 20/28
2025-08-15 11:57:01,310 - uq_methods.implementations.luq - INFO - Processing sample 21/28
2025-08-15 11:57:01,418 - uq_methods.implementations.luq - INFO - Processing sample 22/28
2025-08-15 11:57:01,514 - uq_methods.implementations.luq - INFO - Processing sample 23/28
2025-08-15 11:57:01,603 - uq_methods.implementations.luq - INFO - Processing sample 24/28
2025-08-15 11:57:01,682 - uq_methods.implementations.luq - INFO - Processing sample 25/28
2025-08-15 11:57:01,793 - uq_methods.implementations.luq - INFO - Processing sample 26/28
2025-08-15 11:57:01,881 - uq_methods.implementations.luq - INFO - Processing sample 27/28
2025-08-15 11:57:01,977 - uq_methods.implementations.luq - INFO - Processing sample 28/28
2025-08-15 11:57:02,116 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 19 responses
2025-08-15 11:57:02,119 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 33 sentences
2025-08-15 11:57:02,119 - uq_methods.implementations.luq - INFO - Split text (4609 chars) into 33 sentences
2025-08-15 11:57:02,119 - uq_methods.implementations.luq - INFO - Response 1 split into 33 sentences
2025-08-15 11:57:02,121 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:57:02,121 - uq_methods.implementations.luq - INFO - Split text (6010 chars) into 39 sentences
2025-08-15 11:57:02,121 - uq_methods.implementations.luq - INFO - Response 2 split into 39 sentences
2025-08-15 11:57:02,124 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:02,124 - uq_methods.implementations.luq - INFO - Split text (5201 chars) into 40 sentences
2025-08-15 11:57:02,124 - uq_methods.implementations.luq - INFO - Response 3 split into 40 sentences
2025-08-15 11:57:02,126 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:57:02,126 - uq_methods.implementations.luq - INFO - Split text (5077 chars) into 35 sentences
2025-08-15 11:57:02,126 - uq_methods.implementations.luq - INFO - Response 4 split into 35 sentences
2025-08-15 11:57:02,129 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:57:02,129 - uq_methods.implementations.luq - INFO - Split text (5551 chars) into 46 sentences
2025-08-15 11:57:02,129 - uq_methods.implementations.luq - INFO - Response 5 split into 46 sentences
2025-08-15 11:57:02,131 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:57:02,131 - uq_methods.implementations.luq - INFO - Split text (5159 chars) into 43 sentences
2025-08-15 11:57:02,131 - uq_methods.implementations.luq - INFO - Response 6 split into 43 sentences
2025-08-15 11:57:02,133 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:02,133 - uq_methods.implementations.luq - INFO - Split text (5132 chars) into 42 sentences
2025-08-15 11:57:02,133 - uq_methods.implementations.luq - INFO - Response 7 split into 42 sentences
2025-08-15 11:57:02,136 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:02,136 - uq_methods.implementations.luq - INFO - Split text (5820 chars) into 42 sentences
2025-08-15 11:57:02,136 - uq_methods.implementations.luq - INFO - Response 8 split into 42 sentences
2025-08-15 11:57:02,139 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:57:02,139 - uq_methods.implementations.luq - INFO - Split text (6456 chars) into 43 sentences
2025-08-15 11:57:02,139 - uq_methods.implementations.luq - INFO - Response 9 split into 43 sentences
2025-08-15 11:57:02,141 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:02,141 - uq_methods.implementations.luq - INFO - Split text (6082 chars) into 42 sentences
2025-08-15 11:57:02,141 - uq_methods.implementations.luq - INFO - Response 10 split into 42 sentences
2025-08-15 11:57:02,144 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:57:02,144 - uq_methods.implementations.luq - INFO - Split text (5620 chars) into 35 sentences
2025-08-15 11:57:02,144 - uq_methods.implementations.luq - INFO - Response 11 split into 35 sentences
2025-08-15 11:57:02,146 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:02,146 - uq_methods.implementations.luq - INFO - Split text (5799 chars) into 40 sentences
2025-08-15 11:57:02,146 - uq_methods.implementations.luq - INFO - Response 12 split into 40 sentences
2025-08-15 11:57:02,149 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:02,149 - uq_methods.implementations.luq - INFO - Split text (6285 chars) into 44 sentences
2025-08-15 11:57:02,149 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:57:02,152 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:02,152 - uq_methods.implementations.luq - INFO - Split text (6803 chars) into 47 sentences
2025-08-15 11:57:02,152 - uq_methods.implementations.luq - INFO - Response 14 split into 47 sentences
2025-08-15 11:57:02,154 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 31 sentences
2025-08-15 11:57:02,154 - uq_methods.implementations.luq - INFO - Split text (4477 chars) into 31 sentences
2025-08-15 11:57:02,154 - uq_methods.implementations.luq - INFO - Response 15 split into 31 sentences
2025-08-15 11:57:02,156 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 35 sentences
2025-08-15 11:57:02,156 - uq_methods.implementations.luq - INFO - Split text (4782 chars) into 35 sentences
2025-08-15 11:57:02,156 - uq_methods.implementations.luq - INFO - Response 16 split into 35 sentences
2025-08-15 11:57:02,159 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:57:02,159 - uq_methods.implementations.luq - INFO - Split text (5581 chars) into 37 sentences
2025-08-15 11:57:02,159 - uq_methods.implementations.luq - INFO - Response 17 split into 37 sentences
2025-08-15 11:57:02,161 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:02,161 - uq_methods.implementations.luq - INFO - Split text (5524 chars) into 40 sentences
2025-08-15 11:57:02,161 - uq_methods.implementations.luq - INFO - Response 18 split into 40 sentences
2025-08-15 11:57:02,164 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:57:02,164 - uq_methods.implementations.luq - INFO - Split text (5049 chars) into 30 sentences
2025-08-15 11:57:02,164 - uq_methods.implementations.luq - INFO - Response 19 split into 30 sentences
2025-08-15 11:57:02,164 - uq_methods.implementations.luq - INFO - Processing sample 1/19
2025-08-15 11:57:02,212 - uq_methods.implementations.luq - INFO - Processing sample 2/19
2025-08-15 11:57:02,268 - uq_methods.implementations.luq - INFO - Processing sample 3/19
2025-08-15 11:57:02,324 - uq_methods.implementations.luq - INFO - Processing sample 4/19
2025-08-15 11:57:02,374 - uq_methods.implementations.luq - INFO - Processing sample 5/19
2025-08-15 11:57:02,439 - uq_methods.implementations.luq - INFO - Processing sample 6/19
2025-08-15 11:57:02,501 - uq_methods.implementations.luq - INFO - Processing sample 7/19
2025-08-15 11:57:02,561 - uq_methods.implementations.luq - INFO - Processing sample 8/19
2025-08-15 11:57:02,621 - uq_methods.implementations.luq - INFO - Processing sample 9/19
2025-08-15 11:57:02,681 - uq_methods.implementations.luq - INFO - Processing sample 10/19
2025-08-15 11:57:02,741 - uq_methods.implementations.luq - INFO - Processing sample 11/19
2025-08-15 11:57:02,791 - uq_methods.implementations.luq - INFO - Processing sample 12/19
2025-08-15 11:57:02,847 - uq_methods.implementations.luq - INFO - Processing sample 13/19
2025-08-15 11:57:02,911 - uq_methods.implementations.luq - INFO - Processing sample 14/19
2025-08-15 11:57:02,978 - uq_methods.implementations.luq - INFO - Processing sample 15/19
2025-08-15 11:57:03,022 - uq_methods.implementations.luq - INFO - Processing sample 16/19
2025-08-15 11:57:03,072 - uq_methods.implementations.luq - INFO - Processing sample 17/19
2025-08-15 11:57:03,125 - uq_methods.implementations.luq - INFO - Processing sample 18/19
2025-08-15 11:57:03,182 - uq_methods.implementations.luq - INFO - Processing sample 19/19
2025-08-15 11:57:03,238 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:57:03,240 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:03,240 - uq_methods.implementations.luq - INFO - Split text (5852 chars) into 47 sentences
2025-08-15 11:57:03,240 - uq_methods.implementations.luq - INFO - Response 1 split into 47 sentences
2025-08-15 11:57:03,243 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:57:03,243 - uq_methods.implementations.luq - INFO - Split text (6337 chars) into 53 sentences
2025-08-15 11:57:03,243 - uq_methods.implementations.luq - INFO - Response 2 split into 53 sentences
2025-08-15 11:57:03,245 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:03,245 - uq_methods.implementations.luq - INFO - Split text (5892 chars) into 47 sentences
2025-08-15 11:57:03,245 - uq_methods.implementations.luq - INFO - Response 3 split into 47 sentences
2025-08-15 11:57:03,248 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:57:03,248 - uq_methods.implementations.luq - INFO - Split text (5909 chars) into 51 sentences
2025-08-15 11:57:03,248 - uq_methods.implementations.luq - INFO - Response 4 split into 51 sentences
2025-08-15 11:57:03,250 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:57:03,250 - uq_methods.implementations.luq - INFO - Split text (5224 chars) into 36 sentences
2025-08-15 11:57:03,250 - uq_methods.implementations.luq - INFO - Response 5 split into 36 sentences
2025-08-15 11:57:03,253 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 55 sentences
2025-08-15 11:57:03,253 - uq_methods.implementations.luq - INFO - Split text (6440 chars) into 55 sentences
2025-08-15 11:57:03,253 - uq_methods.implementations.luq - INFO - Response 6 split into 55 sentences
2025-08-15 11:57:03,255 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:03,255 - uq_methods.implementations.luq - INFO - Split text (6482 chars) into 49 sentences
2025-08-15 11:57:03,255 - uq_methods.implementations.luq - INFO - Response 7 split into 49 sentences
2025-08-15 11:57:03,258 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:03,258 - uq_methods.implementations.luq - INFO - Split text (6201 chars) into 45 sentences
2025-08-15 11:57:03,258 - uq_methods.implementations.luq - INFO - Response 8 split into 45 sentences
2025-08-15 11:57:03,262 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 55 sentences
2025-08-15 11:57:03,262 - uq_methods.implementations.luq - INFO - Split text (8300 chars) into 55 sentences
2025-08-15 11:57:03,262 - uq_methods.implementations.luq - INFO - Response 9 split into 55 sentences
2025-08-15 11:57:03,265 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:57:03,265 - uq_methods.implementations.luq - INFO - Split text (7433 chars) into 56 sentences
2025-08-15 11:57:03,265 - uq_methods.implementations.luq - INFO - Response 10 split into 56 sentences
2025-08-15 11:57:03,267 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:03,267 - uq_methods.implementations.luq - INFO - Split text (6484 chars) into 48 sentences
2025-08-15 11:57:03,267 - uq_methods.implementations.luq - INFO - Response 11 split into 48 sentences
2025-08-15 11:57:03,270 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:57:03,270 - uq_methods.implementations.luq - INFO - Split text (6283 chars) into 46 sentences
2025-08-15 11:57:03,270 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:57:03,272 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:03,272 - uq_methods.implementations.luq - INFO - Split text (5290 chars) into 44 sentences
2025-08-15 11:57:03,272 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:57:03,275 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:03,275 - uq_methods.implementations.luq - INFO - Split text (6396 chars) into 48 sentences
2025-08-15 11:57:03,275 - uq_methods.implementations.luq - INFO - Response 14 split into 48 sentences
2025-08-15 11:57:03,277 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:03,277 - uq_methods.implementations.luq - INFO - Split text (5609 chars) into 45 sentences
2025-08-15 11:57:03,277 - uq_methods.implementations.luq - INFO - Response 15 split into 45 sentences
2025-08-15 11:57:03,279 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:57:03,279 - uq_methods.implementations.luq - INFO - Split text (4974 chars) into 39 sentences
2025-08-15 11:57:03,279 - uq_methods.implementations.luq - INFO - Response 16 split into 39 sentences
2025-08-15 11:57:03,281 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:03,281 - uq_methods.implementations.luq - INFO - Split text (4901 chars) into 44 sentences
2025-08-15 11:57:03,281 - uq_methods.implementations.luq - INFO - Response 17 split into 44 sentences
2025-08-15 11:57:03,283 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:03,283 - uq_methods.implementations.luq - INFO - Split text (4762 chars) into 40 sentences
2025-08-15 11:57:03,283 - uq_methods.implementations.luq - INFO - Response 18 split into 40 sentences
2025-08-15 11:57:03,285 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:57:03,285 - uq_methods.implementations.luq - INFO - Split text (4857 chars) into 38 sentences
2025-08-15 11:57:03,285 - uq_methods.implementations.luq - INFO - Response 19 split into 38 sentences
2025-08-15 11:57:03,288 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:57:03,288 - uq_methods.implementations.luq - INFO - Split text (5548 chars) into 41 sentences
2025-08-15 11:57:03,288 - uq_methods.implementations.luq - INFO - Response 20 split into 41 sentences
2025-08-15 11:57:03,290 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:03,290 - uq_methods.implementations.luq - INFO - Split text (6039 chars) into 42 sentences
2025-08-15 11:57:03,290 - uq_methods.implementations.luq - INFO - Response 21 split into 42 sentences
2025-08-15 11:57:03,292 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 30 sentences
2025-08-15 11:57:03,292 - uq_methods.implementations.luq - INFO - Split text (4756 chars) into 30 sentences
2025-08-15 11:57:03,292 - uq_methods.implementations.luq - INFO - Response 22 split into 30 sentences
2025-08-15 11:57:03,295 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:03,295 - uq_methods.implementations.luq - INFO - Split text (5266 chars) into 42 sentences
2025-08-15 11:57:03,295 - uq_methods.implementations.luq - INFO - Response 23 split into 42 sentences
2025-08-15 11:57:03,297 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:03,297 - uq_methods.implementations.luq - INFO - Split text (6358 chars) into 47 sentences
2025-08-15 11:57:03,297 - uq_methods.implementations.luq - INFO - Response 24 split into 47 sentences
2025-08-15 11:57:03,300 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:57:03,300 - uq_methods.implementations.luq - INFO - Split text (6057 chars) into 46 sentences
2025-08-15 11:57:03,300 - uq_methods.implementations.luq - INFO - Response 25 split into 46 sentences
2025-08-15 11:57:03,302 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:03,302 - uq_methods.implementations.luq - INFO - Split text (6673 chars) into 48 sentences
2025-08-15 11:57:03,302 - uq_methods.implementations.luq - INFO - Response 26 split into 48 sentences
2025-08-15 11:57:03,305 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:57:03,305 - uq_methods.implementations.luq - INFO - Split text (5112 chars) into 38 sentences
2025-08-15 11:57:03,305 - uq_methods.implementations.luq - INFO - Response 27 split into 38 sentences
2025-08-15 11:57:03,307 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:03,307 - uq_methods.implementations.luq - INFO - Split text (6605 chars) into 45 sentences
2025-08-15 11:57:03,307 - uq_methods.implementations.luq - INFO - Response 28 split into 45 sentences
2025-08-15 11:57:03,310 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:03,310 - uq_methods.implementations.luq - INFO - Split text (6053 chars) into 49 sentences
2025-08-15 11:57:03,310 - uq_methods.implementations.luq - INFO - Response 29 split into 49 sentences
2025-08-15 11:57:03,312 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:57:03,312 - uq_methods.implementations.luq - INFO - Split text (5497 chars) into 41 sentences
2025-08-15 11:57:03,312 - uq_methods.implementations.luq - INFO - Response 30 split into 41 sentences
2025-08-15 11:57:03,312 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:57:03,421 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:57:03,544 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:57:03,655 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:57:03,776 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:57:03,861 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:57:03,991 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:57:04,108 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:57:04,215 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:57:04,344 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:57:04,477 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:57:04,592 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:57:04,702 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:57:04,807 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:57:04,921 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:57:05,027 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:57:05,120 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:57:05,224 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:57:05,319 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:57:05,409 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:57:05,507 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:57:05,606 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:57:05,677 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:57:05,777 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:57:05,889 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:57:05,996 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:57:06,110 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:57:06,201 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:57:06,307 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:57:06,423 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:57:06,569 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:57:06,571 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:57:06,571 - uq_methods.implementations.luq - INFO - Split text (5232 chars) into 39 sentences
2025-08-15 11:57:06,571 - uq_methods.implementations.luq - INFO - Response 1 split into 39 sentences
2025-08-15 11:57:06,573 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:57:06,573 - uq_methods.implementations.luq - INFO - Split text (4914 chars) into 38 sentences
2025-08-15 11:57:06,573 - uq_methods.implementations.luq - INFO - Response 2 split into 38 sentences
2025-08-15 11:57:06,576 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:57:06,576 - uq_methods.implementations.luq - INFO - Split text (5366 chars) into 41 sentences
2025-08-15 11:57:06,576 - uq_methods.implementations.luq - INFO - Response 3 split into 41 sentences
2025-08-15 11:57:06,578 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:06,578 - uq_methods.implementations.luq - INFO - Split text (5098 chars) into 42 sentences
2025-08-15 11:57:06,578 - uq_methods.implementations.luq - INFO - Response 4 split into 42 sentences
2025-08-15 11:57:06,580 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:57:06,580 - uq_methods.implementations.luq - INFO - Split text (5923 chars) into 52 sentences
2025-08-15 11:57:06,580 - uq_methods.implementations.luq - INFO - Response 5 split into 52 sentences
2025-08-15 11:57:06,582 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 34 sentences
2025-08-15 11:57:06,582 - uq_methods.implementations.luq - INFO - Split text (4446 chars) into 34 sentences
2025-08-15 11:57:06,582 - uq_methods.implementations.luq - INFO - Response 6 split into 34 sentences
2025-08-15 11:57:06,585 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:57:06,585 - uq_methods.implementations.luq - INFO - Split text (6058 chars) into 50 sentences
2025-08-15 11:57:06,585 - uq_methods.implementations.luq - INFO - Response 7 split into 50 sentences
2025-08-15 11:57:06,587 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 59 sentences
2025-08-15 11:57:06,587 - uq_methods.implementations.luq - INFO - Split text (5784 chars) into 59 sentences
2025-08-15 11:57:06,587 - uq_methods.implementations.luq - INFO - Response 8 split into 59 sentences
2025-08-15 11:57:06,590 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:06,590 - uq_methods.implementations.luq - INFO - Split text (5934 chars) into 48 sentences
2025-08-15 11:57:06,590 - uq_methods.implementations.luq - INFO - Response 9 split into 48 sentences
2025-08-15 11:57:06,592 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 50 sentences
2025-08-15 11:57:06,592 - uq_methods.implementations.luq - INFO - Split text (6029 chars) into 50 sentences
2025-08-15 11:57:06,592 - uq_methods.implementations.luq - INFO - Response 10 split into 50 sentences
2025-08-15 11:57:06,595 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:06,595 - uq_methods.implementations.luq - INFO - Split text (5693 chars) into 45 sentences
2025-08-15 11:57:06,595 - uq_methods.implementations.luq - INFO - Response 11 split into 45 sentences
2025-08-15 11:57:06,598 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:57:06,598 - uq_methods.implementations.luq - INFO - Split text (6548 chars) into 52 sentences
2025-08-15 11:57:06,598 - uq_methods.implementations.luq - INFO - Response 12 split into 52 sentences
2025-08-15 11:57:06,600 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:06,600 - uq_methods.implementations.luq - INFO - Split text (5497 chars) into 44 sentences
2025-08-15 11:57:06,600 - uq_methods.implementations.luq - INFO - Response 13 split into 44 sentences
2025-08-15 11:57:06,602 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:57:06,603 - uq_methods.implementations.luq - INFO - Split text (6147 chars) into 54 sentences
2025-08-15 11:57:06,603 - uq_methods.implementations.luq - INFO - Response 14 split into 54 sentences
2025-08-15 11:57:06,605 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:06,605 - uq_methods.implementations.luq - INFO - Split text (6398 chars) into 47 sentences
2025-08-15 11:57:06,605 - uq_methods.implementations.luq - INFO - Response 15 split into 47 sentences
2025-08-15 11:57:06,608 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:06,608 - uq_methods.implementations.luq - INFO - Split text (6030 chars) into 44 sentences
2025-08-15 11:57:06,608 - uq_methods.implementations.luq - INFO - Response 16 split into 44 sentences
2025-08-15 11:57:06,610 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:57:06,610 - uq_methods.implementations.luq - INFO - Split text (5957 chars) into 38 sentences
2025-08-15 11:57:06,610 - uq_methods.implementations.luq - INFO - Response 17 split into 38 sentences
2025-08-15 11:57:06,613 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:06,613 - uq_methods.implementations.luq - INFO - Split text (6264 chars) into 49 sentences
2025-08-15 11:57:06,613 - uq_methods.implementations.luq - INFO - Response 18 split into 49 sentences
2025-08-15 11:57:06,615 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:06,615 - uq_methods.implementations.luq - INFO - Split text (4872 chars) into 45 sentences
2025-08-15 11:57:06,615 - uq_methods.implementations.luq - INFO - Response 19 split into 45 sentences
2025-08-15 11:57:06,617 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 44 sentences
2025-08-15 11:57:06,617 - uq_methods.implementations.luq - INFO - Split text (5382 chars) into 44 sentences
2025-08-15 11:57:06,617 - uq_methods.implementations.luq - INFO - Response 20 split into 44 sentences
2025-08-15 11:57:06,620 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:57:06,620 - uq_methods.implementations.luq - INFO - Split text (5999 chars) into 54 sentences
2025-08-15 11:57:06,620 - uq_methods.implementations.luq - INFO - Response 21 split into 54 sentences
2025-08-15 11:57:06,622 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 63 sentences
2025-08-15 11:57:06,622 - uq_methods.implementations.luq - INFO - Split text (6385 chars) into 63 sentences
2025-08-15 11:57:06,622 - uq_methods.implementations.luq - INFO - Response 22 split into 63 sentences
2025-08-15 11:57:06,625 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:57:06,625 - uq_methods.implementations.luq - INFO - Split text (6773 chars) into 56 sentences
2025-08-15 11:57:06,625 - uq_methods.implementations.luq - INFO - Response 23 split into 56 sentences
2025-08-15 11:57:06,627 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:06,627 - uq_methods.implementations.luq - INFO - Split text (5656 chars) into 48 sentences
2025-08-15 11:57:06,627 - uq_methods.implementations.luq - INFO - Response 24 split into 48 sentences
2025-08-15 11:57:06,630 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 38 sentences
2025-08-15 11:57:06,630 - uq_methods.implementations.luq - INFO - Split text (5119 chars) into 38 sentences
2025-08-15 11:57:06,630 - uq_methods.implementations.luq - INFO - Response 25 split into 38 sentences
2025-08-15 11:57:06,632 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:06,632 - uq_methods.implementations.luq - INFO - Split text (5394 chars) into 49 sentences
2025-08-15 11:57:06,632 - uq_methods.implementations.luq - INFO - Response 26 split into 49 sentences
2025-08-15 11:57:06,634 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:06,634 - uq_methods.implementations.luq - INFO - Split text (5014 chars) into 40 sentences
2025-08-15 11:57:06,634 - uq_methods.implementations.luq - INFO - Response 27 split into 40 sentences
2025-08-15 11:57:06,636 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 40 sentences
2025-08-15 11:57:06,636 - uq_methods.implementations.luq - INFO - Split text (5109 chars) into 40 sentences
2025-08-15 11:57:06,636 - uq_methods.implementations.luq - INFO - Response 28 split into 40 sentences
2025-08-15 11:57:06,639 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 60 sentences
2025-08-15 11:57:06,639 - uq_methods.implementations.luq - INFO - Split text (6184 chars) into 60 sentences
2025-08-15 11:57:06,639 - uq_methods.implementations.luq - INFO - Response 29 split into 60 sentences
2025-08-15 11:57:06,641 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 37 sentences
2025-08-15 11:57:06,641 - uq_methods.implementations.luq - INFO - Split text (4632 chars) into 37 sentences
2025-08-15 11:57:06,641 - uq_methods.implementations.luq - INFO - Response 30 split into 37 sentences
2025-08-15 11:57:06,641 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:57:06,732 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:57:06,820 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:57:06,913 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:57:07,010 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:57:07,128 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:57:07,205 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:57:07,319 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:57:07,454 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:57:07,563 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:57:07,676 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:57:07,783 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:57:07,904 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:57:08,008 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:57:08,134 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:57:08,244 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:57:08,345 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:57:08,433 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:57:08,545 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:57:08,649 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:57:08,750 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:57:08,875 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:57:09,026 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:57:09,156 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:57:09,268 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:57:09,357 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:57:09,472 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:57:09,566 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:57:09,660 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:57:09,801 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:57:09,923 - uq_methods.implementations.luq - INFO - Starting LUQ computation for 30 responses
2025-08-15 11:57:09,926 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:57:09,926 - uq_methods.implementations.luq - INFO - Split text (5218 chars) into 51 sentences
2025-08-15 11:57:09,926 - uq_methods.implementations.luq - INFO - Response 1 split into 51 sentences
2025-08-15 11:57:09,929 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:57:09,929 - uq_methods.implementations.luq - INFO - Split text (6385 chars) into 51 sentences
2025-08-15 11:57:09,929 - uq_methods.implementations.luq - INFO - Response 2 split into 51 sentences
2025-08-15 11:57:09,932 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:09,932 - uq_methods.implementations.luq - INFO - Split text (6768 chars) into 49 sentences
2025-08-15 11:57:09,932 - uq_methods.implementations.luq - INFO - Response 3 split into 49 sentences
2025-08-15 11:57:09,935 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 57 sentences
2025-08-15 11:57:09,935 - uq_methods.implementations.luq - INFO - Split text (7098 chars) into 57 sentences
2025-08-15 11:57:09,935 - uq_methods.implementations.luq - INFO - Response 4 split into 57 sentences
2025-08-15 11:57:09,937 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 43 sentences
2025-08-15 11:57:09,937 - uq_methods.implementations.luq - INFO - Split text (5584 chars) into 43 sentences
2025-08-15 11:57:09,937 - uq_methods.implementations.luq - INFO - Response 5 split into 43 sentences
2025-08-15 11:57:09,941 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 62 sentences
2025-08-15 11:57:09,941 - uq_methods.implementations.luq - INFO - Split text (7180 chars) into 62 sentences
2025-08-15 11:57:09,941 - uq_methods.implementations.luq - INFO - Response 6 split into 62 sentences
2025-08-15 11:57:09,944 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 51 sentences
2025-08-15 11:57:09,944 - uq_methods.implementations.luq - INFO - Split text (7089 chars) into 51 sentences
2025-08-15 11:57:09,944 - uq_methods.implementations.luq - INFO - Response 7 split into 51 sentences
2025-08-15 11:57:09,947 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 63 sentences
2025-08-15 11:57:09,947 - uq_methods.implementations.luq - INFO - Split text (8429 chars) into 63 sentences
2025-08-15 11:57:09,947 - uq_methods.implementations.luq - INFO - Response 8 split into 63 sentences
2025-08-15 11:57:09,950 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:09,950 - uq_methods.implementations.luq - INFO - Split text (5645 chars) into 42 sentences
2025-08-15 11:57:09,950 - uq_methods.implementations.luq - INFO - Response 9 split into 42 sentences
2025-08-15 11:57:09,952 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:09,952 - uq_methods.implementations.luq - INFO - Split text (6314 chars) into 42 sentences
2025-08-15 11:57:09,952 - uq_methods.implementations.luq - INFO - Response 10 split into 42 sentences
2025-08-15 11:57:09,955 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 55 sentences
2025-08-15 11:57:09,956 - uq_methods.implementations.luq - INFO - Split text (7554 chars) into 55 sentences
2025-08-15 11:57:09,956 - uq_methods.implementations.luq - INFO - Response 11 split into 55 sentences
2025-08-15 11:57:09,958 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 46 sentences
2025-08-15 11:57:09,958 - uq_methods.implementations.luq - INFO - Split text (7014 chars) into 46 sentences
2025-08-15 11:57:09,959 - uq_methods.implementations.luq - INFO - Response 12 split into 46 sentences
2025-08-15 11:57:09,961 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:57:09,961 - uq_methods.implementations.luq - INFO - Split text (5456 chars) into 41 sentences
2025-08-15 11:57:09,961 - uq_methods.implementations.luq - INFO - Response 13 split into 41 sentences
2025-08-15 11:57:09,963 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 45 sentences
2025-08-15 11:57:09,963 - uq_methods.implementations.luq - INFO - Split text (5904 chars) into 45 sentences
2025-08-15 11:57:09,963 - uq_methods.implementations.luq - INFO - Response 14 split into 45 sentences
2025-08-15 11:57:09,967 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:09,967 - uq_methods.implementations.luq - INFO - Split text (7147 chars) into 49 sentences
2025-08-15 11:57:09,967 - uq_methods.implementations.luq - INFO - Response 15 split into 49 sentences
2025-08-15 11:57:09,969 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 36 sentences
2025-08-15 11:57:09,969 - uq_methods.implementations.luq - INFO - Split text (4710 chars) into 36 sentences
2025-08-15 11:57:09,969 - uq_methods.implementations.luq - INFO - Response 16 split into 36 sentences
2025-08-15 11:57:09,971 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 42 sentences
2025-08-15 11:57:09,971 - uq_methods.implementations.luq - INFO - Split text (6241 chars) into 42 sentences
2025-08-15 11:57:09,971 - uq_methods.implementations.luq - INFO - Response 17 split into 42 sentences
2025-08-15 11:57:09,974 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 52 sentences
2025-08-15 11:57:09,974 - uq_methods.implementations.luq - INFO - Split text (7567 chars) into 52 sentences
2025-08-15 11:57:09,974 - uq_methods.implementations.luq - INFO - Response 18 split into 52 sentences
2025-08-15 11:57:09,977 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 39 sentences
2025-08-15 11:57:09,977 - uq_methods.implementations.luq - INFO - Split text (5417 chars) into 39 sentences
2025-08-15 11:57:09,977 - uq_methods.implementations.luq - INFO - Response 19 split into 39 sentences
2025-08-15 11:57:09,980 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 57 sentences
2025-08-15 11:57:09,980 - uq_methods.implementations.luq - INFO - Split text (7410 chars) into 57 sentences
2025-08-15 11:57:09,980 - uq_methods.implementations.luq - INFO - Response 20 split into 57 sentences
2025-08-15 11:57:09,983 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:57:09,983 - uq_methods.implementations.luq - INFO - Split text (6722 chars) into 53 sentences
2025-08-15 11:57:09,983 - uq_methods.implementations.luq - INFO - Response 21 split into 53 sentences
2025-08-15 11:57:09,986 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 47 sentences
2025-08-15 11:57:09,986 - uq_methods.implementations.luq - INFO - Split text (6267 chars) into 47 sentences
2025-08-15 11:57:09,986 - uq_methods.implementations.luq - INFO - Response 22 split into 47 sentences
2025-08-15 11:57:09,988 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 49 sentences
2025-08-15 11:57:09,988 - uq_methods.implementations.luq - INFO - Split text (6386 chars) into 49 sentences
2025-08-15 11:57:09,988 - uq_methods.implementations.luq - INFO - Response 23 split into 49 sentences
2025-08-15 11:57:09,991 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:57:09,991 - uq_methods.implementations.luq - INFO - Split text (6534 chars) into 56 sentences
2025-08-15 11:57:09,991 - uq_methods.implementations.luq - INFO - Response 24 split into 56 sentences
2025-08-15 11:57:09,994 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 54 sentences
2025-08-15 11:57:09,994 - uq_methods.implementations.luq - INFO - Split text (7050 chars) into 54 sentences
2025-08-15 11:57:09,994 - uq_methods.implementations.luq - INFO - Response 25 split into 54 sentences
2025-08-15 11:57:09,997 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 61 sentences
2025-08-15 11:57:09,997 - uq_methods.implementations.luq - INFO - Split text (6816 chars) into 61 sentences
2025-08-15 11:57:09,997 - uq_methods.implementations.luq - INFO - Response 26 split into 61 sentences
2025-08-15 11:57:10,000 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 48 sentences
2025-08-15 11:57:10,000 - uq_methods.implementations.luq - INFO - Split text (5782 chars) into 48 sentences
2025-08-15 11:57:10,000 - uq_methods.implementations.luq - INFO - Response 27 split into 48 sentences
2025-08-15 11:57:10,002 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 41 sentences
2025-08-15 11:57:10,003 - uq_methods.implementations.luq - INFO - Split text (5810 chars) into 41 sentences
2025-08-15 11:57:10,003 - uq_methods.implementations.luq - INFO - Response 28 split into 41 sentences
2025-08-15 11:57:10,005 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 56 sentences
2025-08-15 11:57:10,005 - uq_methods.implementations.luq - INFO - Split text (6529 chars) into 56 sentences
2025-08-15 11:57:10,005 - uq_methods.implementations.luq - INFO - Response 29 split into 56 sentences
2025-08-15 11:57:10,008 - uq_methods.implementations.luq - INFO - Used sentence-splitter, found 53 sentences
2025-08-15 11:57:10,008 - uq_methods.implementations.luq - INFO - Split text (6546 chars) into 53 sentences
2025-08-15 11:57:10,008 - uq_methods.implementations.luq - INFO - Response 30 split into 53 sentences
2025-08-15 11:57:10,008 - uq_methods.implementations.luq - INFO - Processing sample 1/30
2025-08-15 11:57:10,132 - uq_methods.implementations.luq - INFO - Processing sample 2/30
2025-08-15 11:57:10,253 - uq_methods.implementations.luq - INFO - Processing sample 3/30
2025-08-15 11:57:10,369 - uq_methods.implementations.luq - INFO - Processing sample 4/30
2025-08-15 11:57:10,504 - uq_methods.implementations.luq - INFO - Processing sample 5/30
2025-08-15 11:57:10,606 - uq_methods.implementations.luq - INFO - Processing sample 6/30
2025-08-15 11:57:10,754 - uq_methods.implementations.luq - INFO - Processing sample 7/30
2025-08-15 11:57:10,875 - uq_methods.implementations.luq - INFO - Processing sample 8/30
2025-08-15 11:57:11,023 - uq_methods.implementations.luq - INFO - Processing sample 9/30
2025-08-15 11:57:11,122 - uq_methods.implementations.luq - INFO - Processing sample 10/30
2025-08-15 11:57:11,221 - uq_methods.implementations.luq - INFO - Processing sample 11/30
2025-08-15 11:57:11,350 - uq_methods.implementations.luq - INFO - Processing sample 12/30
2025-08-15 11:57:11,458 - uq_methods.implementations.luq - INFO - Processing sample 13/30
2025-08-15 11:57:11,554 - uq_methods.implementations.luq - INFO - Processing sample 14/30
2025-08-15 11:57:11,661 - uq_methods.implementations.luq - INFO - Processing sample 15/30
2025-08-15 11:57:11,776 - uq_methods.implementations.luq - INFO - Processing sample 16/30
2025-08-15 11:57:11,861 - uq_methods.implementations.luq - INFO - Processing sample 17/30
2025-08-15 11:57:11,961 - uq_methods.implementations.luq - INFO - Processing sample 18/30
2025-08-15 11:57:12,084 - uq_methods.implementations.luq - INFO - Processing sample 19/30
2025-08-15 11:57:12,176 - uq_methods.implementations.luq - INFO - Processing sample 20/30
2025-08-15 11:57:12,310 - uq_methods.implementations.luq - INFO - Processing sample 21/30
2025-08-15 11:57:12,435 - uq_methods.implementations.luq - INFO - Processing sample 22/30
2025-08-15 11:57:12,546 - uq_methods.implementations.luq - INFO - Processing sample 23/30
2025-08-15 11:57:12,661 - uq_methods.implementations.luq - INFO - Processing sample 24/30
2025-08-15 11:57:12,794 - uq_methods.implementations.luq - INFO - Processing sample 25/30
2025-08-15 11:57:12,921 - uq_methods.implementations.luq - INFO - Processing sample 26/30
2025-08-15 11:57:13,065 - uq_methods.implementations.luq - INFO - Processing sample 27/30
2025-08-15 11:57:13,178 - uq_methods.implementations.luq - INFO - Processing sample 28/30
2025-08-15 11:57:13,275 - uq_methods.implementations.luq - INFO - Processing sample 29/30
2025-08-15 11:57:13,408 - uq_methods.implementations.luq - INFO - Processing sample 30/30
2025-08-15 11:57:13,572 - uq_analysis.progress_manager - INFO - [PROGRESS] counterfactual_qa/counterfactual_data: 10/10 groups (100.0%) in 33.6s - Success: 10, Failed: 0, Skipped: 0
2025-08-15 11:57:13,572 - uq_analysis.progress_manager - INFO - [ETA] counterfactual_qa/counterfactual_data: 0.0s remaining
2025-08-15 11:57:13,572 - uq_analysis.progress_manager - INFO - Finished task counterfactual_qa/counterfactual_data in 33.61s
2025-08-15 11:57:13,572 - uq_analysis.progress_manager - INFO - [COMPLETED] counterfactual_qa/counterfactual_data: 10/10 groups (100.0%) in 33.6s - Success: 10, Failed: 0, Skipped: 0
2025-08-15 11:57:13,576 - uq_analysis.progress_manager - INFO - Analysis completed in 35.91s
2025-08-15 11:57:13,576 - uq_analysis.progress_manager - INFO - [FINAL SUMMARY] Groups: 10/10, Methods: 10/1, Success: 10, Failed: 0, Skipped: 0, Time: 35.9s
2025-08-15 11:57:13,576 - __main__ - INFO - UQ analysis completed successfully
2025-08-15 11:57:13,576 - uq_analysis.data_processor - INFO - Disconnected from MongoDB

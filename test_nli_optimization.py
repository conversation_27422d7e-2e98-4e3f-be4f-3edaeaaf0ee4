#!/usr/bin/env python3
"""
测试NLI缓存优化功能
"""

import os
import sys
import tempfile
import shutil
import sqlite3
from core.nli_calculator import CachedNLICalculator, NLIResult
from tools.nli_cache_optimizer import NLICacheOptimizer


class MockNLICalculator(CachedNLICalculator):
    """模拟的NLI计算器，用于测试缓存功能而不需要实际的模型"""

    def __init__(self, model_name: str = "mock-model", verbose: bool = False, use_sqlite: bool = True):
        # 跳过父类的模型初始化
        self.model_name = model_name
        self.verbose = verbose

        # 缓存配置
        self.cache_dir = "cache"
        self.use_sqlite = use_sqlite

        if use_sqlite:
            self.db_file = os.path.join(self.cache_dir, "nli_cache.db")
        else:
            self.csv_cache_file = os.path.join(self.cache_dir, "nli_results_cache.csv")

        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)

        # 初始化缓存后端
        if use_sqlite:
            self._init_sqlite_cache()

    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """模拟NLI分数计算"""
        # 基于文本长度和内容生成模拟分数
        len1, len2 = len(text1), len(text2)

        # 简单的模拟逻辑：相似长度的文本有更高的entailment分数
        len_diff = abs(len1 - len2)
        entailment = max(0.1, 1.0 - len_diff / max(len1, len2, 1))

        # 确保分数总和为1
        remaining = 1.0 - entailment
        neutral = remaining * 0.6
        contradiction = remaining * 0.4

        return NLIResult(
            entailment=entailment,
            neutral=neutral,
            contradiction=contradiction,
            logits=None
        )


def test_nli_cache_optimization():
    """测试NLI缓存优化功能"""
    
    # 创建临时目录用于测试
    test_cache_dir = tempfile.mkdtemp(prefix="nli_test_")
    
    try:
        print(f"Testing NLI cache optimization in: {test_cache_dir}")
        
        # 1. 创建一些测试数据
        print("\n=== Step 1: Creating test cache data ===")
        calculator = MockNLICalculator(
            model_name="test-model",
            verbose=True,
            use_sqlite=True
        )
        calculator.cache_dir = test_cache_dir
        calculator.db_file = os.path.join(test_cache_dir, "nli_cache.db")
        calculator._init_sqlite_cache()
        
        # 添加一些测试数据
        test_cases = [
            ("This is a short text", "This is another short text"),
            ("This is a much longer text that contains more information and details about various topics", 
             "This is a different longer text with different content and information"),
            ("Short", "Brief"),
            ("A very long text " * 50, "Another very long text " * 45),  # 大文本测试
        ]
        
        print(f"Computing NLI scores for {len(test_cases)} test cases...")
        for i, (text1, text2) in enumerate(test_cases):
            try:
                result = calculator.compute_nli_scores_cached(text1, text2)
                print(f"  Test case {i+1}: entailment={result.entailment:.3f}")
            except Exception as e:
                print(f"  Test case {i+1}: Error - {e}")
        
        # 2. 分析缓存
        print("\n=== Step 2: Analyzing cache ===")
        optimizer = NLICacheOptimizer(test_cache_dir)
        analysis = optimizer.analyze_cache()
        
        if "error" in analysis:
            print(f"Analysis error: {analysis['error']}")
            return False
            
        print(f"Cache is optimized: {analysis['is_optimized']}")
        print(f"Total entries: {analysis['total_entries']}")
        print(f"Database size: {analysis['database_size_bytes']} bytes")
        print(f"Average text length: {analysis['avg_text_length']:.1f} chars")
        
        # 3. 测试统计功能
        print("\n=== Step 3: Testing statistics ===")
        stats = calculator.get_cache_stats()
        
        if "error" in stats:
            print(f"Stats error: {stats['error']}")
        else:
            print(f"Total entries: {stats['total_entries']}")
            print(f"Unique models: {stats['unique_models']}")
            print(f"Text1 avg length: {stats['text_length_stats']['text1']['avg']}")
            print(f"Text2 avg length: {stats['text_length_stats']['text2']['avg']}")
        
        # 4. 测试缓存命中
        print("\n=== Step 4: Testing cache hits ===")
        text1, text2 = test_cases[0]
        
        # 第一次调用（应该从缓存读取）
        result1 = calculator.compute_nli_scores_cached(text1, text2)
        print(f"First call result: entailment={result1.entailment:.3f}")
        
        # 第二次调用（应该从缓存读取）
        result2 = calculator.compute_nli_scores_cached(text1, text2)
        print(f"Second call result: entailment={result2.entailment:.3f}")
        
        # 验证结果一致
        if abs(result1.entailment - result2.entailment) < 1e-6:
            print("✅ Cache consistency test passed")
        else:
            print("❌ Cache consistency test failed")
            return False
        
        # 5. 测试hash功能
        print("\n=== Step 5: Testing hash functions ===")
        test_text = "This is a test text for hashing"
        
        md5_hash = calculator.get_text_hash(test_text, 'md5')
        sha256_hash = calculator.get_text_hash(test_text, 'sha256')
        
        print(f"MD5 hash: {md5_hash}")
        print(f"SHA256 hash: {sha256_hash}")
        
        # 验证hash一致性
        md5_hash2 = calculator.get_text_hash(test_text, 'md5')
        if md5_hash == md5_hash2:
            print("✅ Hash consistency test passed")
        else:
            print("❌ Hash consistency test failed")
            return False
        
        # 6. 测试数据库压缩
        print("\n=== Step 6: Testing database vacuum ===")
        vacuum_result = optimizer.vacuum_database()
        
        if "error" in vacuum_result:
            print(f"Vacuum error: {vacuum_result['error']}")
        else:
            print(f"Space reclaimed: {vacuum_result['space_reclaimed_bytes']} bytes")
        
        print("\n✅ All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(test_cache_dir)
            print(f"\nCleaned up test directory: {test_cache_dir}")
        except Exception as e:
            print(f"Warning: Failed to clean up test directory: {e}")


def test_migration_scenario():
    """测试从旧版本缓存迁移的场景"""
    print("\n" + "="*50)
    print("Testing migration from legacy cache format")
    print("="*50)
    
    # 这个测试需要一个包含旧格式数据的数据库
    # 由于我们没有现成的旧数据，这里只是演示框架
    print("Migration test would require existing legacy cache data")
    print("In a real scenario, this would test the _migrate_legacy_cache method")
    
    return True


if __name__ == "__main__":
    print("NLI Cache Optimization Test Suite")
    print("=" * 50)
    
    success = True
    
    # 运行主要测试
    success &= test_nli_cache_optimization()
    
    # 运行迁移测试
    success &= test_migration_scenario()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)

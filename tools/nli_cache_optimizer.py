#!/usr/bin/env python3
"""
NLI缓存优化工具
用于管理、优化和监控NLI缓存数据库
"""

import os
import sys
import sqlite3
import argparse
import hashlib
from typing import Dict, Any
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.nli_calculator import CachedNLICalculator


class NLICacheOptimizer:
    """NLI缓存优化器"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = cache_dir
        self.db_file = os.path.join(cache_dir, "nli_cache.db")
        
    def analyze_cache(self) -> Dict[str, Any]:
        """分析缓存数据库"""
        if not os.path.exists(self.db_file):
            return {"error": "Cache database not found"}
            
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(nli_cache)")
            columns = [col[1] for col in cursor.fetchall()]
            
            is_optimized = 'text1_hash' in columns and 'text1' not in columns
            
            # 基本统计
            cursor.execute("SELECT COUNT(*) FROM nli_cache")
            total_entries = cursor.fetchone()[0]
            
            if is_optimized:
                # 优化版本的统计
                cursor.execute("""
                    SELECT 
                        AVG(text1_length + text2_length) as avg_total_len,
                        SUM(text1_length + text2_length) as total_text_len,
                        COUNT(DISTINCT model_name) as unique_models
                    FROM nli_cache
                """)
                stats = cursor.fetchone()
                
                estimated_space_saved = stats[1] * 0.8 if stats[1] else 0  # 估算节省的空间
                
            else:
                # 旧版本的统计
                cursor.execute("""
                    SELECT 
                        AVG(LENGTH(text1) + LENGTH(text2)) as avg_total_len,
                        SUM(LENGTH(text1) + LENGTH(text2)) as total_text_len,
                        COUNT(DISTINCT model_name) as unique_models
                    FROM nli_cache
                """)
                stats = cursor.fetchone()
                estimated_space_saved = 0
            
            conn.close()
            
            return {
                "is_optimized": is_optimized,
                "total_entries": total_entries,
                "unique_models": stats[2] if stats else 0,
                "avg_text_length": round(stats[0] or 0, 2),
                "total_text_length": stats[1] or 0,
                "estimated_space_saved_bytes": int(estimated_space_saved),
                "database_size_bytes": os.path.getsize(self.db_file),
                "columns": columns
            }
            
        except Exception as e:
            return {"error": f"Failed to analyze cache: {e}"}
    
    def optimize_cache(self) -> Dict[str, Any]:
        """优化缓存数据库"""
        analysis = self.analyze_cache()
        
        if "error" in analysis:
            return analysis
            
        if analysis["is_optimized"]:
            return {"message": "Cache is already optimized", "analysis": analysis}
        
        try:
            # 使用CachedNLICalculator的迁移功能
            calculator = CachedNLICalculator(verbose=True)
            
            # 重新分析以获取优化后的统计
            new_analysis = self.analyze_cache()
            
            return {
                "message": "Cache optimization completed successfully",
                "before": analysis,
                "after": new_analysis,
                "space_saved_bytes": analysis["database_size_bytes"] - new_analysis["database_size_bytes"]
            }
            
        except Exception as e:
            return {"error": f"Failed to optimize cache: {e}"}
    
    def vacuum_database(self) -> Dict[str, Any]:
        """压缩数据库以回收空间"""
        if not os.path.exists(self.db_file):
            return {"error": "Cache database not found"}
            
        try:
            size_before = os.path.getsize(self.db_file)
            
            conn = sqlite3.connect(self.db_file)
            conn.execute("VACUUM")
            conn.close()
            
            size_after = os.path.getsize(self.db_file)
            
            return {
                "message": "Database vacuum completed",
                "size_before_bytes": size_before,
                "size_after_bytes": size_after,
                "space_reclaimed_bytes": size_before - size_after
            }
            
        except Exception as e:
            return {"error": f"Failed to vacuum database: {e}"}


def main():
    parser = argparse.ArgumentParser(description="NLI Cache Optimizer")
    parser.add_argument("--cache-dir", default="cache", help="Cache directory path")
    parser.add_argument("--action", choices=["analyze", "optimize", "vacuum", "stats"], 
                       default="analyze", help="Action to perform")
    
    args = parser.parse_args()
    
    optimizer = NLICacheOptimizer(args.cache_dir)
    
    if args.action == "analyze":
        result = optimizer.analyze_cache()
        print("=== NLI Cache Analysis ===")
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Optimized: {'Yes' if result['is_optimized'] else 'No'}")
            print(f"Total entries: {result['total_entries']:,}")
            print(f"Unique models: {result['unique_models']}")
            print(f"Average text length: {result['avg_text_length']:.1f} chars")
            print(f"Total text length: {result['total_text_length']:,} chars")
            print(f"Database size: {result['database_size_bytes']:,} bytes")
            if result['estimated_space_saved_bytes'] > 0:
                print(f"Estimated space saved: {result['estimated_space_saved_bytes']:,} bytes")
    
    elif args.action == "optimize":
        result = optimizer.optimize_cache()
        print("=== NLI Cache Optimization ===")
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(result['message'])
            if 'space_saved_bytes' in result:
                print(f"Space saved: {result['space_saved_bytes']:,} bytes")
    
    elif args.action == "vacuum":
        result = optimizer.vacuum_database()
        print("=== Database Vacuum ===")
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(result['message'])
            print(f"Space reclaimed: {result['space_reclaimed_bytes']:,} bytes")
    
    elif args.action == "stats":
        # 使用CachedNLICalculator获取详细统计
        try:
            calculator = CachedNLICalculator(verbose=False)
            stats = calculator.get_cache_stats()
            
            print("=== Detailed Cache Statistics ===")
            if "error" in stats:
                print(f"Error: {stats['error']}")
            else:
                print(f"Total entries: {stats['total_entries']:,}")
                print(f"Unique models: {stats['unique_models']}")
                print("\nModel distribution:")
                for model, count in stats['model_distribution'].items():
                    print(f"  {model}: {count:,} entries")
                
                print("\nText length statistics:")
                print(f"  Text1 - Avg: {stats['text_length_stats']['text1']['avg']:.1f}, "
                      f"Min: {stats['text_length_stats']['text1']['min']}, "
                      f"Max: {stats['text_length_stats']['text1']['max']}")
                print(f"  Text2 - Avg: {stats['text_length_stats']['text2']['avg']:.1f}, "
                      f"Min: {stats['text_length_stats']['text2']['min']}, "
                      f"Max: {stats['text_length_stats']['text2']['max']}")
                      
        except Exception as e:
            print(f"Error getting detailed stats: {e}")


if __name__ == "__main__":
    main()
